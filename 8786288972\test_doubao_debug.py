#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包AI自动发送功能测试脚本
用于验证修改后的选择器和JavaScript代码
"""

def test_doubao_selectors():
    """测试豆包AI的选择器配置"""
    
    # 输入框选择器
    input_selectors = [
        "textarea",
        ".input-area textarea", 
        "#chat-input",
        ".chat-input",
        "[contenteditable='true']",
        ".ProseMirror",
        "div[contenteditable='true']",
        ".chat-input-area textarea",
        ".message-input",
        ".input-box textarea",
        "[data-testid='chat-input']",
        ".composer-input"
    ]
    
    # 发送按钮选择器
    button_selectors = [
        'button[aria-label="发送"]',
        'button[title="发送"]',
        '.send-button',
        'button[type="submit"]',
        '.submit-btn',
        '[data-testid="send-button"]',
        'button[aria-label="Send Message"]',
        '.send-btn',
        '.submit-button',
        'button.send',
        '[data-testid="submit-button"]',
        'button[title="Send"]',
        '.chat-send-button'
    ]
    
    print("豆包AI输入框选择器:")
    for i, selector in enumerate(input_selectors, 1):
        print(f"  {i}. {selector}")
    
    print("\n豆包AI发送按钮选择器:")
    for i, selector in enumerate(button_selectors, 1):
        print(f"  {i}. {selector}")
    
    # 生成JavaScript测试代码
    js_test_code = """
    // 豆包AI DOM结构调试代码
    function testDoubaoSelectors() {
        console.log('=== 豆包AI选择器测试 ===');
        
        // 测试输入框选择器
        var inputSelectors = %s;
        console.log('测试输入框选择器:');
        inputSelectors.forEach(function(selector, index) {
            var elements = document.querySelectorAll(selector);
            console.log((index + 1) + '. ' + selector + ' -> ' + elements.length + '个元素', elements);
        });
        
        // 测试发送按钮选择器
        var buttonSelectors = %s;
        console.log('\\n测试发送按钮选择器:');
        buttonSelectors.forEach(function(selector, index) {
            var elements = document.querySelectorAll(selector);
            console.log((index + 1) + '. ' + selector + ' -> ' + elements.length + '个元素', elements);
        });
        
        // 显示页面中所有可能的输入元素
        console.log('\\n=== 页面中所有输入元素 ===');
        var allInputs = document.querySelectorAll('input, textarea, [contenteditable="true"]');
        allInputs.forEach(function(el, index) {
            console.log((index + 1) + '.', el.tagName, el.className, el.id, el);
        });
        
        // 显示页面中所有按钮
        console.log('\\n=== 页面中所有按钮 ===');
        var allButtons = document.querySelectorAll('button');
        allButtons.forEach(function(btn, index) {
            if (index < 20) { // 只显示前20个按钮
                console.log((index + 1) + '.', btn.textContent.trim(), 
                           'aria-label:', btn.getAttribute('aria-label'),
                           'title:', btn.getAttribute('title'),
                           'class:', btn.className, btn);
            }
        });
    }
    
    // 将函数添加到全局作用域
    window.testDoubaoSelectors = testDoubaoSelectors;
    
    // 自动运行测试
    testDoubaoSelectors();
    """ % (str(input_selectors).replace("'", '"'), str(button_selectors).replace("'", '"'))
    
    print("\n=== JavaScript测试代码 ===")
    print("在豆包AI页面的控制台中运行以下代码:")
    print(js_test_code)
    
    return js_test_code

def generate_debug_bookmarklet():
    """生成调试书签代码"""
    bookmarklet = """javascript:(function(){
        var inputSelectors = ["textarea", ".input-area textarea", "#chat-input", ".chat-input", "[contenteditable='true']", ".ProseMirror", "div[contenteditable='true']", ".chat-input-area textarea", ".message-input", ".input-box textarea", "[data-testid='chat-input']", ".composer-input"];
        var buttonSelectors = ["button[aria-label=\\"发送\\"]", "button[title=\\"发送\\"]", ".send-button", "button[type=\\"submit\\"]", ".submit-btn", "[data-testid=\\"send-button\\"]", "button[aria-label=\\"Send Message\\"]", ".send-btn", ".submit-button", "button.send", "[data-testid=\\"submit-button\\"]", "button[title=\\"Send\\"]", ".chat-send-button"];
        
        console.log("=== 豆包AI调试信息 ===");
        console.log("输入框测试:");
        inputSelectors.forEach(function(sel, i) {
            var els = document.querySelectorAll(sel);
            if (els.length > 0) console.log((i+1) + ". " + sel + " -> " + els.length + "个", els);
        });
        
        console.log("按钮测试:");
        buttonSelectors.forEach(function(sel, i) {
            var els = document.querySelectorAll(sel);
            if (els.length > 0) console.log((i+1) + ". " + sel + " -> " + els.length + "个", els);
        });
        
        alert("调试信息已输出到控制台，请按F12查看");
    })();"""
    
    print("\n=== 调试书签 ===")
    print("将以下代码保存为书签，在豆包AI页面点击即可调试:")
    print(bookmarklet)
    
    return bookmarklet

if __name__ == "__main__":
    print("豆包AI自动发送功能调试工具")
    print("=" * 50)
    
    # 测试选择器
    js_code = test_doubao_selectors()
    
    # 生成书签
    bookmarklet = generate_debug_bookmarklet()
    
    print("\n" + "=" * 50)
    print("使用说明:")
    print("1. 在豆包AI页面打开开发者工具 (F12)")
    print("2. 在控制台中粘贴上面的JavaScript代码并运行")
    print("3. 或者使用调试书签快速检查")
    print("4. 查看控制台输出，找到实际可用的选择器")
    print("5. 根据结果更新dock_web_view.py中的选择器配置")
