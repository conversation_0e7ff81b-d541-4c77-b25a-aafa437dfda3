"""Wrapper for http://developer.download.nvidia.com/opengl/includes/glxext.h

Generated by tools/gengl.py.
Do not modify this file.
"""

from ctypes import (
    CFUNCTYPE, POINTER, Structure, c_char, c_ulong, c_float, c_int,
    c_int64, c_int32, c_ubyte, c_uint, c_long,
)
from pyglet.gl.lib import link_GLX as _link_function
from pyglet.gl.lib import c_void


# BEGIN GENERATED CONTENT (do not edit below this line)

# This content is generated by tools/gengl.py.
# Wrapper for http://developer.download.nvidia.com/opengl/includes/glxext.h

import pyglet.libs.x11.xlib
import pyglet.gl.glx

# H (/usr/include/GL/glx.h:26)
# ARB_get_proc_address (/usr/include/GL/glx.h:317)
# GLXEXT_LEGACY (/usr/include/GL/glx.h:334)
GLX_GLXEXT_VERSION = 10 	# GL/glxext.h:57
# ARB_get_proc_address (GL/glxext.h:59)
# ARB_multisample (GL/glxext.h:62)
GLX_SAMPLE_BUFFERS_ARB = 100000 	# GL/glxext.h:63
GLX_SAMPLES_ARB = 100001 	# GL/glxext.h:64
# ARB_fbconfig_float (GL/glxext.h:67)
GLX_RGBA_FLOAT_TYPE_ARB = 8377 	# GL/glxext.h:68
GLX_RGBA_FLOAT_BIT_ARB = 4 	# GL/glxext.h:69
# SGIS_multisample (GL/glxext.h:72)
GLX_SAMPLE_BUFFERS_SGIS = 100000 	# GL/glxext.h:73
GLX_SAMPLES_SGIS = 100001 	# GL/glxext.h:74
# EXT_visual_info (GL/glxext.h:77)
GLX_X_VISUAL_TYPE_EXT = 34 	# GL/glxext.h:78
GLX_TRANSPARENT_TYPE_EXT = 35 	# GL/glxext.h:79
GLX_TRANSPARENT_INDEX_VALUE_EXT = 36 	# GL/glxext.h:80
GLX_TRANSPARENT_RED_VALUE_EXT = 37 	# GL/glxext.h:81
GLX_TRANSPARENT_GREEN_VALUE_EXT = 38 	# GL/glxext.h:82
GLX_TRANSPARENT_BLUE_VALUE_EXT = 39 	# GL/glxext.h:83
GLX_TRANSPARENT_ALPHA_VALUE_EXT = 40 	# GL/glxext.h:84
GLX_NONE_EXT = 32768 	# GL/glxext.h:85
GLX_TRUE_COLOR_EXT = 32770 	# GL/glxext.h:86
GLX_DIRECT_COLOR_EXT = 32771 	# GL/glxext.h:87
GLX_PSEUDO_COLOR_EXT = 32772 	# GL/glxext.h:88
GLX_STATIC_COLOR_EXT = 32773 	# GL/glxext.h:89
GLX_GRAY_SCALE_EXT = 32774 	# GL/glxext.h:90
GLX_STATIC_GRAY_EXT = 32775 	# GL/glxext.h:91
GLX_TRANSPARENT_RGB_EXT = 32776 	# GL/glxext.h:92
GLX_TRANSPARENT_INDEX_EXT = 32777 	# GL/glxext.h:93
# SGI_swap_control (GL/glxext.h:96)
# SGI_video_sync (GL/glxext.h:99)
# SGI_make_current_read (GL/glxext.h:102)
# SGIX_video_source (GL/glxext.h:105)
# EXT_visual_rating (GL/glxext.h:108)
GLX_VISUAL_CAVEAT_EXT = 32 	# GL/glxext.h:109
GLX_SLOW_VISUAL_EXT = 32769 	# GL/glxext.h:110
GLX_NON_CONFORMANT_VISUAL_EXT = 32781 	# GL/glxext.h:111
# EXT_import_context (GL/glxext.h:115)
GLX_SHARE_CONTEXT_EXT = 32778 	# GL/glxext.h:116
GLX_VISUAL_ID_EXT = 32779 	# GL/glxext.h:117
GLX_SCREEN_EXT = 32780 	# GL/glxext.h:118
# SGIX_fbconfig (GL/glxext.h:121)
GLX_WINDOW_BIT_SGIX = 1 	# GL/glxext.h:122
GLX_PIXMAP_BIT_SGIX = 2 	# GL/glxext.h:123
GLX_RGBA_BIT_SGIX = 1 	# GL/glxext.h:124
GLX_COLOR_INDEX_BIT_SGIX = 2 	# GL/glxext.h:125
GLX_DRAWABLE_TYPE_SGIX = 32784 	# GL/glxext.h:126
GLX_RENDER_TYPE_SGIX = 32785 	# GL/glxext.h:127
GLX_X_RENDERABLE_SGIX = 32786 	# GL/glxext.h:128
GLX_FBCONFIG_ID_SGIX = 32787 	# GL/glxext.h:129
GLX_RGBA_TYPE_SGIX = 32788 	# GL/glxext.h:130
GLX_COLOR_INDEX_TYPE_SGIX = 32789 	# GL/glxext.h:131
# SGIX_pbuffer (GL/glxext.h:135)
GLX_PBUFFER_BIT_SGIX = 4 	# GL/glxext.h:136
GLX_BUFFER_CLOBBER_MASK_SGIX = 134217728 	# GL/glxext.h:137
GLX_FRONT_LEFT_BUFFER_BIT_SGIX = 1 	# GL/glxext.h:138
GLX_FRONT_RIGHT_BUFFER_BIT_SGIX = 2 	# GL/glxext.h:139
GLX_BACK_LEFT_BUFFER_BIT_SGIX = 4 	# GL/glxext.h:140
GLX_BACK_RIGHT_BUFFER_BIT_SGIX = 8 	# GL/glxext.h:141
GLX_AUX_BUFFERS_BIT_SGIX = 16 	# GL/glxext.h:142
GLX_DEPTH_BUFFER_BIT_SGIX = 32 	# GL/glxext.h:143
GLX_STENCIL_BUFFER_BIT_SGIX = 64 	# GL/glxext.h:144
GLX_ACCUM_BUFFER_BIT_SGIX = 128 	# GL/glxext.h:145
GLX_SAMPLE_BUFFERS_BIT_SGIX = 256 	# GL/glxext.h:146
GLX_MAX_PBUFFER_WIDTH_SGIX = 32790 	# GL/glxext.h:147
GLX_MAX_PBUFFER_HEIGHT_SGIX = 32791 	# GL/glxext.h:148
GLX_MAX_PBUFFER_PIXELS_SGIX = 32792 	# GL/glxext.h:149
GLX_OPTIMAL_PBUFFER_WIDTH_SGIX = 32793 	# GL/glxext.h:150
GLX_OPTIMAL_PBUFFER_HEIGHT_SGIX = 32794 	# GL/glxext.h:151
GLX_PRESERVED_CONTENTS_SGIX = 32795 	# GL/glxext.h:152
GLX_LARGEST_PBUFFER_SGIX = 32796 	# GL/glxext.h:153
GLX_WIDTH_SGIX = 32797 	# GL/glxext.h:154
GLX_HEIGHT_SGIX = 32798 	# GL/glxext.h:155
GLX_EVENT_MASK_SGIX = 32799 	# GL/glxext.h:156
GLX_DAMAGED_SGIX = 32800 	# GL/glxext.h:157
GLX_SAVED_SGIX = 32801 	# GL/glxext.h:158
GLX_WINDOW_SGIX = 32802 	# GL/glxext.h:159
GLX_PBUFFER_SGIX = 32803 	# GL/glxext.h:160
# SGI_cushion (GL/glxext.h:163)
# SGIX_video_resize (GL/glxext.h:166)
GLX_SYNC_FRAME_SGIX = 0 	# GL/glxext.h:167
GLX_SYNC_SWAP_SGIX = 1 	# GL/glxext.h:168
# SGIX_dmbuffer (GL/glxext.h:171)
GLX_DIGITAL_MEDIA_PBUFFER_SGIX = 32804 	# GL/glxext.h:172
# SGIX_swap_group (GL/glxext.h:175)
# SGIX_swap_barrier (GL/glxext.h:178)
# SGIS_blended_overlay (GL/glxext.h:181)
GLX_BLENDED_RGBA_SGIS = 32805 	# GL/glxext.h:182
# SGIS_shared_multisample (GL/glxext.h:185)
GLX_MULTISAMPLE_SUB_RECT_WIDTH_SGIS = 32806 	# GL/glxext.h:186
GLX_MULTISAMPLE_SUB_RECT_HEIGHT_SGIS = 32807 	# GL/glxext.h:187
# SUN_get_transparent_index (GL/glxext.h:190)
# 3DFX_multisample (GL/glxext.h:193)
GLX_SAMPLE_BUFFERS_3DFX = 32848 	# GL/glxext.h:194
GLX_SAMPLES_3DFX = 32849 	# GL/glxext.h:195
# MESA_copy_sub_buffer (GL/glxext.h:198)
# MESA_pixmap_colormap (GL/glxext.h:201)
# MESA_release_buffers (GL/glxext.h:204)
# MESA_set_3dfx_mode (GL/glxext.h:207)
GLX_3DFX_WINDOW_MODE_MESA = 1 	# GL/glxext.h:208
GLX_3DFX_FULLSCREEN_MODE_MESA = 2 	# GL/glxext.h:209
# SGIX_visual_select_group (GL/glxext.h:212)
GLX_VISUAL_SELECT_GROUP_SGIX = 32808 	# GL/glxext.h:213
# OML_swap_method (GL/glxext.h:216)
GLX_SWAP_METHOD_OML = 32864 	# GL/glxext.h:217
GLX_SWAP_EXCHANGE_OML = 32865 	# GL/glxext.h:218
GLX_SWAP_COPY_OML = 32866 	# GL/glxext.h:219
GLX_SWAP_UNDEFINED_OML = 32867 	# GL/glxext.h:220
# OML_sync_control (GL/glxext.h:223)
# NV_float_buffer (GL/glxext.h:226)
GLX_FLOAT_COMPONENTS_NV = 8368 	# GL/glxext.h:227
# SGIX_hyperpipe (GL/glxext.h:230)
GLX_HYPERPIPE_PIPE_NAME_LENGTH_SGIX = 80 	# GL/glxext.h:231
GLX_BAD_HYPERPIPE_CONFIG_SGIX = 91 	# GL/glxext.h:232
GLX_BAD_HYPERPIPE_SGIX = 92 	# GL/glxext.h:233
GLX_HYPERPIPE_DISPLAY_PIPE_SGIX = 1 	# GL/glxext.h:234
GLX_HYPERPIPE_RENDER_PIPE_SGIX = 2 	# GL/glxext.h:235
GLX_PIPE_RECT_SGIX = 1 	# GL/glxext.h:236
GLX_PIPE_RECT_LIMITS_SGIX = 2 	# GL/glxext.h:237
GLX_HYPERPIPE_STEREO_SGIX = 3 	# GL/glxext.h:238
GLX_HYPERPIPE_PIXEL_AVERAGE_SGIX = 4 	# GL/glxext.h:239
GLX_HYPERPIPE_ID_SGIX = 32816 	# GL/glxext.h:240
# MESA_agp_offset (GL/glxext.h:243)
# ARB_get_proc_address (GL/glxext.h:249)
# SGIX_video_source (GL/glxext.h:256)
XID = pyglet.libs.x11.xlib.XID
GLXVideoSourceSGIX = XID 	# GL/glxext.h:257
# SGIX_fbconfig (GL/glxext.h:260)
GLXFBConfigIDSGIX = XID 	# GL/glxext.h:261
class struct___GLXFBConfigRec(Structure):
    __slots__ = [
    ]
struct___GLXFBConfigRec._fields_ = [
    ('_opaque_struct', c_int)
]

GLXFBConfigSGIX = POINTER(struct___GLXFBConfigRec) 	# GL/glxext.h:262
# SGIX_pbuffer (GL/glxext.h:265)
GLXPbufferSGIX = XID 	# GL/glxext.h:266
class struct_anon_106(Structure):
    __slots__ = [
        'type',
        'serial',
        'send_event',
        'display',
        'drawable',
        'event_type',
        'draw_type',
        'mask',
        'x',
        'y',
        'width',
        'height',
        'count',
    ]
Display = pyglet.libs.x11.xlib.Display
GLXDrawable = pyglet.gl.glx.GLXDrawable
struct_anon_106._fields_ = [
    ('type', c_int),
    ('serial', c_ulong),
    ('send_event', c_int),
    ('display', POINTER(Display)),
    ('drawable', GLXDrawable),
    ('event_type', c_int),
    ('draw_type', c_int),
    ('mask', c_uint),
    ('x', c_int),
    ('y', c_int),
    ('width', c_int),
    ('height', c_int),
    ('count', c_int),
]

GLXBufferClobberEventSGIX = struct_anon_106 	# GL/glxext.h:279
# NV_swap_group (GL/glxext.h:282)
# NV_video_out (GL/glxext.h:285)
GLXVideoDeviceNV = c_uint 	# GL/glxext.h:290
GLX_VIDEO_OUT_COLOR_NV = 8387 	# GL/glxext.h:293
GLX_VIDEO_OUT_ALPHA_NV = 8388 	# GL/glxext.h:294
GLX_VIDEO_OUT_DEPTH_NV = 8389 	# GL/glxext.h:295
GLX_VIDEO_OUT_COLOR_AND_ALPHA_NV = 8390 	# GL/glxext.h:296
GLX_VIDEO_OUT_COLOR_AND_DEPTH_NV = 8391 	# GL/glxext.h:297
GLX_VIDEO_OUT_FRAME_NV = 8392 	# GL/glxext.h:300
GLX_VIDEO_OUT_FIELD_1_NV = 8393 	# GL/glxext.h:301
GLX_VIDEO_OUT_FIELD_2_NV = 8394 	# GL/glxext.h:302
# EXT_texture_from_pixmap (GL/glxext.h:305)
GLX_BIND_TO_TEXTURE_RGB_EXT = 8400 	# GL/glxext.h:307
GLX_BIND_TO_TEXTURE_RGBA_EXT = 8401 	# GL/glxext.h:308
GLX_BIND_TO_MIPMAP_TEXTURE_EXT = 8402 	# GL/glxext.h:309
GLX_BIND_TO_TEXTURE_TARGETS_EXT = 8403 	# GL/glxext.h:310
GLX_Y_INVERTED_EXT = 8404 	# GL/glxext.h:311
GLX_TEXTURE_FORMAT_EXT = 8405 	# GL/glxext.h:314
GLX_TEXTURE_TARGET_EXT = 8406 	# GL/glxext.h:315
GLX_MIPMAP_TEXTURE_EXT = 8407 	# GL/glxext.h:316
GLX_TEXTURE_FORMAT_NONE_EXT = 8408 	# GL/glxext.h:319
GLX_TEXTURE_FORMAT_RGB_EXT = 8409 	# GL/glxext.h:320
GLX_TEXTURE_FORMAT_RGBA_EXT = 8410 	# GL/glxext.h:321
GLX_TEXTURE_1D_BIT_EXT = 1 	# GL/glxext.h:324
GLX_TEXTURE_2D_BIT_EXT = 2 	# GL/glxext.h:325
GLX_TEXTURE_RECTANGLE_BIT_EXT = 4 	# GL/glxext.h:326
GLX_TEXTURE_1D_EXT = 8411 	# GL/glxext.h:329
GLX_TEXTURE_2D_EXT = 8412 	# GL/glxext.h:330
GLX_TEXTURE_RECTANGLE_EXT = 8413 	# GL/glxext.h:331
GLX_FRONT_LEFT_EXT = 8414 	# GL/glxext.h:337
GLX_FRONT_RIGHT_EXT = 8415 	# GL/glxext.h:338
GLX_BACK_LEFT_EXT = 8416 	# GL/glxext.h:339
GLX_BACK_RIGHT_EXT = 8417 	# GL/glxext.h:340
GLX_FRONT_EXT = 8414 	# GL/glxext.h:341
GLX_BACK_EXT = 8416 	# GL/glxext.h:342
GLX_AUX0_EXT = 8418 	# GL/glxext.h:343
GLX_AUX1_EXT = 8419 	# GL/glxext.h:344
GLX_AUX2_EXT = 8420 	# GL/glxext.h:345
GLX_AUX3_EXT = 8421 	# GL/glxext.h:346
GLX_AUX4_EXT = 8422 	# GL/glxext.h:347
GLX_AUX5_EXT = 8423 	# GL/glxext.h:348
GLX_AUX6_EXT = 8424 	# GL/glxext.h:349
GLX_AUX7_EXT = 8425 	# GL/glxext.h:350
GLX_AUX8_EXT = 8426 	# GL/glxext.h:351
GLX_AUX9_EXT = 8427 	# GL/glxext.h:352
# ARB_get_proc_address (GL/glxext.h:373)
# ARB_multisample (GL/glxext.h:377)
GLX_ARB_multisample = 1 	# GL/glxext.h:378
# ARB_fbconfig_float (GL/glxext.h:381)
GLX_ARB_fbconfig_float = 1 	# GL/glxext.h:382
# SGIS_multisample (GL/glxext.h:385)
GLX_SGIS_multisample = 1 	# GL/glxext.h:386
# EXT_visual_info (GL/glxext.h:389)
GLX_EXT_visual_info = 1 	# GL/glxext.h:390
# SGI_swap_control (GL/glxext.h:393)
GLX_SGI_swap_control = 1 	# GL/glxext.h:394
# GL/glxext.h:396
glXSwapIntervalSGI = _link_function('glXSwapIntervalSGI', c_int, [c_int], 'SGI_swap_control')

PFNGLXSWAPINTERVALSGIPROC = CFUNCTYPE(c_int, c_int) 	# GL/glxext.h:398
# SGI_video_sync (GL/glxext.h:401)
GLX_SGI_video_sync = 1 	# GL/glxext.h:402
# GL/glxext.h:404
glXGetVideoSyncSGI = _link_function('glXGetVideoSyncSGI', c_int, [POINTER(c_uint)], 'SGI_video_sync')

# GL/glxext.h:405
glXWaitVideoSyncSGI = _link_function('glXWaitVideoSyncSGI', c_int, [c_int, c_int, POINTER(c_uint)], 'SGI_video_sync')

# GL/glxext.h:406
glXGetRefreshRateSGI = _link_function('glXGetRefreshRateSGI', c_int, [POINTER(c_uint)], 'SGI_video_sync')

PFNGLXGETVIDEOSYNCSGIPROC = CFUNCTYPE(c_int, POINTER(c_uint)) 	# GL/glxext.h:408
PFNGLXWAITVIDEOSYNCSGIPROC = CFUNCTYPE(c_int, c_int, c_int, POINTER(c_uint)) 	# GL/glxext.h:409
PFNGLXGETREFRESHRATESGIPROC = CFUNCTYPE(c_int, POINTER(c_uint)) 	# GL/glxext.h:410
# SGI_make_current_read (GL/glxext.h:413)
GLX_SGI_make_current_read = 1 	# GL/glxext.h:414
GLXContext = pyglet.gl.glx.GLXContext
# GL/glxext.h:416
glXMakeCurrentReadSGI = _link_function('glXMakeCurrentReadSGI', c_int, [POINTER(Display), GLXDrawable, GLXDrawable, GLXContext], 'SGI_make_current_read')

# GL/glxext.h:417
glXGetCurrentReadDrawableSGI = _link_function('glXGetCurrentReadDrawableSGI', GLXDrawable, [], 'SGI_make_current_read')

PFNGLXMAKECURRENTREADSGIPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, GLXDrawable, GLXContext) 	# GL/glxext.h:419
PFNGLXGETCURRENTREADDRAWABLESGIPROC = CFUNCTYPE(GLXDrawable) 	# GL/glxext.h:420
# SGIX_video_source (GL/glxext.h:423)
GLX_SGIX_video_source = 1 	# GL/glxext.h:424
# EXT_visual_rating (GL/glxext.h:435)
GLX_EXT_visual_rating = 1 	# GL/glxext.h:436
# EXT_import_context (GL/glxext.h:439)
GLX_EXT_import_context = 1 	# GL/glxext.h:440
# GL/glxext.h:442
glXGetCurrentDisplayEXT = _link_function('glXGetCurrentDisplayEXT', POINTER(Display), [], 'EXT_import_context')

# GL/glxext.h:443
glXQueryContextInfoEXT = _link_function('glXQueryContextInfoEXT', c_int, [POINTER(Display), GLXContext, c_int, POINTER(c_int)], 'EXT_import_context')

GLXContextID = pyglet.gl.glx.GLXContextID
# GL/glxext.h:444
glXGetContextIDEXT = _link_function('glXGetContextIDEXT', GLXContextID, [GLXContext], 'EXT_import_context')

# GL/glxext.h:445
glXImportContextEXT = _link_function('glXImportContextEXT', GLXContext, [POINTER(Display), GLXContextID], 'EXT_import_context')

# GL/glxext.h:446
glXFreeContextEXT = _link_function('glXFreeContextEXT', None, [POINTER(Display), GLXContext], 'EXT_import_context')

PFNGLXGETCURRENTDISPLAYEXTPROC = CFUNCTYPE(POINTER(Display)) 	# GL/glxext.h:448
PFNGLXQUERYCONTEXTINFOEXTPROC = CFUNCTYPE(c_int, POINTER(Display), GLXContext, c_int, POINTER(c_int)) 	# GL/glxext.h:449
PFNGLXGETCONTEXTIDEXTPROC = CFUNCTYPE(GLXContextID, GLXContext) 	# GL/glxext.h:450
PFNGLXIMPORTCONTEXTEXTPROC = CFUNCTYPE(GLXContext, POINTER(Display), GLXContextID) 	# GL/glxext.h:451
PFNGLXFREECONTEXTEXTPROC = CFUNCTYPE(None, POINTER(Display), GLXContext) 	# GL/glxext.h:452
# SGIX_fbconfig (GL/glxext.h:455)
GLX_SGIX_fbconfig = 1 	# GL/glxext.h:456
# GL/glxext.h:458
glXGetFBConfigAttribSGIX = _link_function('glXGetFBConfigAttribSGIX', c_int, [POINTER(Display), GLXFBConfigSGIX, c_int, POINTER(c_int)], 'SGIX_fbconfig')

# GL/glxext.h:459
glXChooseFBConfigSGIX = _link_function('glXChooseFBConfigSGIX', POINTER(GLXFBConfigSGIX), [POINTER(Display), c_int, POINTER(c_int), POINTER(c_int)], 'SGIX_fbconfig')

GLXPixmap = pyglet.gl.glx.GLXPixmap
Pixmap = pyglet.libs.x11.xlib.Pixmap
# GL/glxext.h:460
glXCreateGLXPixmapWithConfigSGIX = _link_function('glXCreateGLXPixmapWithConfigSGIX', GLXPixmap, [POINTER(Display), GLXFBConfigSGIX, Pixmap], 'SGIX_fbconfig')

# GL/glxext.h:461
glXCreateContextWithConfigSGIX = _link_function('glXCreateContextWithConfigSGIX', GLXContext, [POINTER(Display), GLXFBConfigSGIX, c_int, GLXContext, c_int], 'SGIX_fbconfig')

XVisualInfo = pyglet.libs.x11.xlib.XVisualInfo
# GL/glxext.h:462
glXGetVisualFromFBConfigSGIX = _link_function('glXGetVisualFromFBConfigSGIX', POINTER(XVisualInfo), [POINTER(Display), GLXFBConfigSGIX], 'SGIX_fbconfig')

# GL/glxext.h:463
glXGetFBConfigFromVisualSGIX = _link_function('glXGetFBConfigFromVisualSGIX', GLXFBConfigSGIX, [POINTER(Display), POINTER(XVisualInfo)], 'SGIX_fbconfig')

PFNGLXGETFBCONFIGATTRIBSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), GLXFBConfigSGIX, c_int, POINTER(c_int)) 	# GL/glxext.h:465
PFNGLXCHOOSEFBCONFIGSGIXPROC = CFUNCTYPE(POINTER(GLXFBConfigSGIX), POINTER(Display), c_int, POINTER(c_int), POINTER(c_int)) 	# GL/glxext.h:466
PFNGLXCREATEGLXPIXMAPWITHCONFIGSGIXPROC = CFUNCTYPE(GLXPixmap, POINTER(Display), GLXFBConfigSGIX, Pixmap) 	# GL/glxext.h:467
PFNGLXCREATECONTEXTWITHCONFIGSGIXPROC = CFUNCTYPE(GLXContext, POINTER(Display), GLXFBConfigSGIX, c_int, GLXContext, c_int) 	# GL/glxext.h:468
PFNGLXGETVISUALFROMFBCONFIGSGIXPROC = CFUNCTYPE(POINTER(XVisualInfo), POINTER(Display), GLXFBConfigSGIX) 	# GL/glxext.h:469
PFNGLXGETFBCONFIGFROMVISUALSGIXPROC = CFUNCTYPE(GLXFBConfigSGIX, POINTER(Display), POINTER(XVisualInfo)) 	# GL/glxext.h:470
# SGIX_pbuffer (GL/glxext.h:473)
GLX_SGIX_pbuffer = 1 	# GL/glxext.h:474
# GL/glxext.h:476
glXCreateGLXPbufferSGIX = _link_function('glXCreateGLXPbufferSGIX', GLXPbufferSGIX, [POINTER(Display), GLXFBConfigSGIX, c_uint, c_uint, POINTER(c_int)], 'SGIX_pbuffer')

# GL/glxext.h:477
glXDestroyGLXPbufferSGIX = _link_function('glXDestroyGLXPbufferSGIX', None, [POINTER(Display), GLXPbufferSGIX], 'SGIX_pbuffer')

# GL/glxext.h:478
glXQueryGLXPbufferSGIX = _link_function('glXQueryGLXPbufferSGIX', c_int, [POINTER(Display), GLXPbufferSGIX, c_int, POINTER(c_uint)], 'SGIX_pbuffer')

# GL/glxext.h:479
glXSelectEventSGIX = _link_function('glXSelectEventSGIX', None, [POINTER(Display), GLXDrawable, c_ulong], 'SGIX_pbuffer')

# GL/glxext.h:480
glXGetSelectedEventSGIX = _link_function('glXGetSelectedEventSGIX', None, [POINTER(Display), GLXDrawable, POINTER(c_ulong)], 'SGIX_pbuffer')

PFNGLXCREATEGLXPBUFFERSGIXPROC = CFUNCTYPE(GLXPbufferSGIX, POINTER(Display), GLXFBConfigSGIX, c_uint, c_uint, POINTER(c_int)) 	# GL/glxext.h:482
PFNGLXDESTROYGLXPBUFFERSGIXPROC = CFUNCTYPE(None, POINTER(Display), GLXPbufferSGIX) 	# GL/glxext.h:483
PFNGLXQUERYGLXPBUFFERSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), GLXPbufferSGIX, c_int, POINTER(c_uint)) 	# GL/glxext.h:484
PFNGLXSELECTEVENTSGIXPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, c_ulong) 	# GL/glxext.h:485
PFNGLXGETSELECTEDEVENTSGIXPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, POINTER(c_ulong)) 	# GL/glxext.h:486
# SGI_cushion (GL/glxext.h:489)
GLX_SGI_cushion = 1 	# GL/glxext.h:490
Window = pyglet.libs.x11.xlib.Window
# GL/glxext.h:492
glXCushionSGI = _link_function('glXCushionSGI', None, [POINTER(Display), Window, c_float], 'SGI_cushion')

PFNGLXCUSHIONSGIPROC = CFUNCTYPE(None, POINTER(Display), Window, c_float) 	# GL/glxext.h:494
# SGIX_video_resize (GL/glxext.h:497)
GLX_SGIX_video_resize = 1 	# GL/glxext.h:498
# GL/glxext.h:500
glXBindChannelToWindowSGIX = _link_function('glXBindChannelToWindowSGIX', c_int, [POINTER(Display), c_int, c_int, Window], 'SGIX_video_resize')

# GL/glxext.h:501
glXChannelRectSGIX = _link_function('glXChannelRectSGIX', c_int, [POINTER(Display), c_int, c_int, c_int, c_int, c_int, c_int], 'SGIX_video_resize')

# GL/glxext.h:502
glXQueryChannelRectSGIX = _link_function('glXQueryChannelRectSGIX', c_int, [POINTER(Display), c_int, c_int, POINTER(c_int), POINTER(c_int), POINTER(c_int), POINTER(c_int)], 'SGIX_video_resize')

# GL/glxext.h:503
glXQueryChannelDeltasSGIX = _link_function('glXQueryChannelDeltasSGIX', c_int, [POINTER(Display), c_int, c_int, POINTER(c_int), POINTER(c_int), POINTER(c_int), POINTER(c_int)], 'SGIX_video_resize')

GLenum = c_uint 	# /usr/include/GL/gl.h:153
# GL/glxext.h:504
glXChannelRectSyncSGIX = _link_function('glXChannelRectSyncSGIX', c_int, [POINTER(Display), c_int, c_int, GLenum], 'SGIX_video_resize')

PFNGLXBINDCHANNELTOWINDOWSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, Window) 	# GL/glxext.h:506
PFNGLXCHANNELRECTSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, c_int, c_int, c_int, c_int) 	# GL/glxext.h:507
PFNGLXQUERYCHANNELRECTSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, POINTER(c_int), POINTER(c_int), POINTER(c_int), POINTER(c_int)) 	# GL/glxext.h:508
PFNGLXQUERYCHANNELDELTASSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, POINTER(c_int), POINTER(c_int), POINTER(c_int), POINTER(c_int)) 	# GL/glxext.h:509
PFNGLXCHANNELRECTSYNCSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, GLenum) 	# GL/glxext.h:510
# SGIX_dmbuffer (GL/glxext.h:513)
GLX_SGIX_dmbuffer = 1 	# GL/glxext.h:514
# SGIX_swap_group (GL/glxext.h:523)
GLX_SGIX_swap_group = 1 	# GL/glxext.h:524
# GL/glxext.h:526
glXJoinSwapGroupSGIX = _link_function('glXJoinSwapGroupSGIX', None, [POINTER(Display), GLXDrawable, GLXDrawable], 'SGIX_swap_group')

PFNGLXJOINSWAPGROUPSGIXPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, GLXDrawable) 	# GL/glxext.h:528
# SGIX_swap_barrier (GL/glxext.h:531)
GLX_SGIX_swap_barrier = 1 	# GL/glxext.h:532
# GL/glxext.h:534
glXBindSwapBarrierSGIX = _link_function('glXBindSwapBarrierSGIX', None, [POINTER(Display), GLXDrawable, c_int], 'SGIX_swap_barrier')

# GL/glxext.h:535
glXQueryMaxSwapBarriersSGIX = _link_function('glXQueryMaxSwapBarriersSGIX', c_int, [POINTER(Display), c_int, POINTER(c_int)], 'SGIX_swap_barrier')

PFNGLXBINDSWAPBARRIERSGIXPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, c_int) 	# GL/glxext.h:537
PFNGLXQUERYMAXSWAPBARRIERSSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, POINTER(c_int)) 	# GL/glxext.h:538
# SUN_get_transparent_index (GL/glxext.h:541)
GLX_SUN_get_transparent_index = 1 	# GL/glxext.h:542
# GL/glxext.h:544
glXGetTransparentIndexSUN = _link_function('glXGetTransparentIndexSUN', c_int, [POINTER(Display), Window, Window, POINTER(c_long)], 'SUN_get_transparent_index')

PFNGLXGETTRANSPARENTINDEXSUNPROC = CFUNCTYPE(c_int, POINTER(Display), Window, Window, POINTER(c_long)) 	# GL/glxext.h:546
# MESA_copy_sub_buffer (GL/glxext.h:549)
GLX_MESA_copy_sub_buffer = 1 	# GL/glxext.h:550
# GL/glxext.h:552
glXCopySubBufferMESA = _link_function('glXCopySubBufferMESA', None, [POINTER(Display), GLXDrawable, c_int, c_int, c_int, c_int], 'MESA_copy_sub_buffer')

PFNGLXCOPYSUBBUFFERMESAPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, c_int, c_int, c_int, c_int) 	# GL/glxext.h:554
# MESA_pixmap_colormap (GL/glxext.h:557)
GLX_MESA_pixmap_colormap = 1 	# GL/glxext.h:558
Colormap = pyglet.libs.x11.xlib.Colormap
# GL/glxext.h:560
glXCreateGLXPixmapMESA = _link_function('glXCreateGLXPixmapMESA', GLXPixmap, [POINTER(Display), POINTER(XVisualInfo), Pixmap, Colormap], 'MESA_pixmap_colormap')

PFNGLXCREATEGLXPIXMAPMESAPROC = CFUNCTYPE(GLXPixmap, POINTER(Display), POINTER(XVisualInfo), Pixmap, Colormap) 	# GL/glxext.h:562
# MESA_release_buffers (GL/glxext.h:565)
GLX_MESA_release_buffers = 1 	# GL/glxext.h:566
# GL/glxext.h:568
glXReleaseBuffersMESA = _link_function('glXReleaseBuffersMESA', c_int, [POINTER(Display), GLXDrawable], 'MESA_release_buffers')

PFNGLXRELEASEBUFFERSMESAPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable) 	# GL/glxext.h:570
# MESA_set_3dfx_mode (GL/glxext.h:573)
GLX_MESA_set_3dfx_mode = 1 	# GL/glxext.h:574
# GL/glxext.h:576
glXSet3DfxModeMESA = _link_function('glXSet3DfxModeMESA', c_int, [c_int], 'MESA_set_3dfx_mode')

PFNGLXSET3DFXMODEMESAPROC = CFUNCTYPE(c_int, c_int) 	# GL/glxext.h:578
# SGIX_visual_select_group (GL/glxext.h:581)
GLX_SGIX_visual_select_group = 1 	# GL/glxext.h:582
# OML_swap_method (GL/glxext.h:585)
GLX_OML_swap_method = 1 	# GL/glxext.h:586
# OML_sync_control (GL/glxext.h:589)
GLX_OML_sync_control = 1 	# GL/glxext.h:590
# GL/glxext.h:592
glXGetSyncValuesOML = _link_function('glXGetSyncValuesOML', c_int, [POINTER(Display), GLXDrawable, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)], 'OML_sync_control')

# GL/glxext.h:593
glXGetMscRateOML = _link_function('glXGetMscRateOML', c_int, [POINTER(Display), GLXDrawable, POINTER(c_int32), POINTER(c_int32)], 'OML_sync_control')

# GL/glxext.h:594
glXSwapBuffersMscOML = _link_function('glXSwapBuffersMscOML', c_int64, [POINTER(Display), GLXDrawable, c_int64, c_int64, c_int64], 'OML_sync_control')

# GL/glxext.h:595
glXWaitForMscOML = _link_function('glXWaitForMscOML', c_int, [POINTER(Display), GLXDrawable, c_int64, c_int64, c_int64, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)], 'OML_sync_control')

# GL/glxext.h:596
glXWaitForSbcOML = _link_function('glXWaitForSbcOML', c_int, [POINTER(Display), GLXDrawable, c_int64, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)], 'OML_sync_control')

PFNGLXGETSYNCVALUESOMLPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)) 	# GL/glxext.h:598
PFNGLXGETMSCRATEOMLPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, POINTER(c_int32), POINTER(c_int32)) 	# GL/glxext.h:599
PFNGLXSWAPBUFFERSMSCOMLPROC = CFUNCTYPE(c_int64, POINTER(Display), GLXDrawable, c_int64, c_int64, c_int64) 	# GL/glxext.h:600
PFNGLXWAITFORMSCOMLPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, c_int64, c_int64, c_int64, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)) 	# GL/glxext.h:601
PFNGLXWAITFORSBCOMLPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, c_int64, POINTER(c_int64), POINTER(c_int64), POINTER(c_int64)) 	# GL/glxext.h:602
# NV_float_buffer (GL/glxext.h:605)
GLX_NV_float_buffer = 1 	# GL/glxext.h:606
# SGIX_hyperpipe (GL/glxext.h:609)
GLX_SGIX_hyperpipe = 1 	# GL/glxext.h:610
class struct_anon_107(Structure):
    __slots__ = [
        'pipeName',
        'networkId',
    ]
struct_anon_107._fields_ = [
    ('pipeName', c_char * 80),
    ('networkId', c_int),
]

GLXHyperpipeNetworkSGIX = struct_anon_107 	# GL/glxext.h:615
class struct_anon_108(Structure):
    __slots__ = [
        'pipeName',
        'channel',
        'participationType',
        'timeSlice',
    ]
struct_anon_108._fields_ = [
    ('pipeName', c_char * 80),
    ('channel', c_int),
    ('participationType', c_uint),
    ('timeSlice', c_int),
]

GLXHyperpipeConfigSGIX = struct_anon_108 	# GL/glxext.h:623
class struct_anon_109(Structure):
    __slots__ = [
        'pipeName',
        'srcXOrigin',
        'srcYOrigin',
        'srcWidth',
        'srcHeight',
        'destXOrigin',
        'destYOrigin',
        'destWidth',
        'destHeight',
    ]
struct_anon_109._fields_ = [
    ('pipeName', c_char * 80),
    ('srcXOrigin', c_int),
    ('srcYOrigin', c_int),
    ('srcWidth', c_int),
    ('srcHeight', c_int),
    ('destXOrigin', c_int),
    ('destYOrigin', c_int),
    ('destWidth', c_int),
    ('destHeight', c_int),
]

GLXPipeRect = struct_anon_109 	# GL/glxext.h:629
class struct_anon_110(Structure):
    __slots__ = [
        'pipeName',
        'XOrigin',
        'YOrigin',
        'maxHeight',
        'maxWidth',
    ]
struct_anon_110._fields_ = [
    ('pipeName', c_char * 80),
    ('XOrigin', c_int),
    ('YOrigin', c_int),
    ('maxHeight', c_int),
    ('maxWidth', c_int),
]

GLXPipeRectLimits = struct_anon_110 	# GL/glxext.h:634
# GL/glxext.h:637
glXQueryHyperpipeNetworkSGIX = _link_function('glXQueryHyperpipeNetworkSGIX', POINTER(GLXHyperpipeNetworkSGIX), [POINTER(Display), POINTER(c_int)], 'SGIX_hyperpipe')

# GL/glxext.h:638
glXHyperpipeConfigSGIX = _link_function('glXHyperpipeConfigSGIX', c_int, [POINTER(Display), c_int, c_int, POINTER(GLXHyperpipeConfigSGIX), POINTER(c_int)], 'SGIX_hyperpipe')

# GL/glxext.h:639
glXQueryHyperpipeConfigSGIX = _link_function('glXQueryHyperpipeConfigSGIX', POINTER(GLXHyperpipeConfigSGIX), [POINTER(Display), c_int, POINTER(c_int)], 'SGIX_hyperpipe')

# GL/glxext.h:640
glXDestroyHyperpipeConfigSGIX = _link_function('glXDestroyHyperpipeConfigSGIX', c_int, [POINTER(Display), c_int], 'SGIX_hyperpipe')

# GL/glxext.h:641
glXBindHyperpipeSGIX = _link_function('glXBindHyperpipeSGIX', c_int, [POINTER(Display), c_int], 'SGIX_hyperpipe')

# GL/glxext.h:642
glXQueryHyperpipeBestAttribSGIX = _link_function('glXQueryHyperpipeBestAttribSGIX', c_int, [POINTER(Display), c_int, c_int, c_int, POINTER(None), POINTER(None)], 'SGIX_hyperpipe')

# GL/glxext.h:643
glXHyperpipeAttribSGIX = _link_function('glXHyperpipeAttribSGIX', c_int, [POINTER(Display), c_int, c_int, c_int, POINTER(None)], 'SGIX_hyperpipe')

# GL/glxext.h:644
glXQueryHyperpipeAttribSGIX = _link_function('glXQueryHyperpipeAttribSGIX', c_int, [POINTER(Display), c_int, c_int, c_int, POINTER(None)], 'SGIX_hyperpipe')

PFNGLXQUERYHYPERPIPENETWORKSGIXPROC = CFUNCTYPE(POINTER(GLXHyperpipeNetworkSGIX), POINTER(Display), POINTER(c_int)) 	# GL/glxext.h:646
PFNGLXHYPERPIPECONFIGSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, POINTER(GLXHyperpipeConfigSGIX), POINTER(c_int)) 	# GL/glxext.h:647
PFNGLXQUERYHYPERPIPECONFIGSGIXPROC = CFUNCTYPE(POINTER(GLXHyperpipeConfigSGIX), POINTER(Display), c_int, POINTER(c_int)) 	# GL/glxext.h:648
PFNGLXDESTROYHYPERPIPECONFIGSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int) 	# GL/glxext.h:649
PFNGLXBINDHYPERPIPESGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int) 	# GL/glxext.h:650
PFNGLXQUERYHYPERPIPEBESTATTRIBSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, c_int, POINTER(None), POINTER(None)) 	# GL/glxext.h:651
PFNGLXHYPERPIPEATTRIBSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, c_int, POINTER(None)) 	# GL/glxext.h:652
PFNGLXQUERYHYPERPIPEATTRIBSGIXPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, c_int, POINTER(None)) 	# GL/glxext.h:653
# MESA_agp_offset (GL/glxext.h:656)
GLX_MESA_agp_offset = 1 	# GL/glxext.h:657
# GL/glxext.h:659
glXGetAGPOffsetMESA = _link_function('glXGetAGPOffsetMESA', c_uint, [POINTER(None)], 'MESA_agp_offset')

PFNGLXGETAGPOFFSETMESAPROC = CFUNCTYPE(c_uint, POINTER(None)) 	# GL/glxext.h:661
# NV_vertex_array_range (GL/glxext.h:667)
GLX_NV_vertex_array_range = 1 	# GL/glxext.h:668
GLsizei = pyglet.gl.glx.GLsizei
GLfloat = pyglet.gl.glx.GLfloat
# GL/glxext.h:670
glXAllocateMemoryNV = _link_function('glXAllocateMemoryNV', POINTER(c_void), [GLsizei, GLfloat, GLfloat, GLfloat], 'NV_vertex_array_range')

GLvoid = pyglet.gl.glx.GLvoid
# GL/glxext.h:673
glXFreeMemoryNV = _link_function('glXFreeMemoryNV', None, [POINTER(GLvoid)], 'NV_vertex_array_range')

PFNGLXALLOCATEMEMORYNVPROC = pyglet.gl.glx.PFNGLXALLOCATEMEMORYNVPROC
PFNGLXFREEMEMORYNVPROC = pyglet.gl.glx.PFNGLXFREEMEMORYNVPROC
# NV_swap_group (GL/glxext.h:683)
GLX_NV_swap_group = 1 	# GL/glxext.h:684
GLuint = pyglet.gl.glx.GLuint
# GL/glxext.h:686
glXJoinSwapGroupNV = _link_function('glXJoinSwapGroupNV', c_int, [POINTER(Display), GLXDrawable, GLuint], 'NV_swap_group')

# GL/glxext.h:689
glXBindSwapBarrierNV = _link_function('glXBindSwapBarrierNV', c_int, [POINTER(Display), GLuint, GLuint], 'NV_swap_group')

# GL/glxext.h:691
glXQuerySwapGroupNV = _link_function('glXQuerySwapGroupNV', c_int, [POINTER(Display), GLXDrawable, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# GL/glxext.h:694
glXQueryMaxSwapGroupsNV = _link_function('glXQueryMaxSwapGroupsNV', c_int, [POINTER(Display), c_int, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# GL/glxext.h:697
glXQueryFrameCountNV = _link_function('glXQueryFrameCountNV', c_int, [POINTER(Display), c_int, POINTER(GLuint)], 'NV_swap_group')

# GL/glxext.h:699
glXResetFrameCountNV = _link_function('glXResetFrameCountNV', c_int, [POINTER(Display), c_int], 'NV_swap_group')

PFNGLXJOINSWAPGROUPNVPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, GLuint) 	# GL/glxext.h:701
PFNGLXBINDSWAPBARRIERNVPROC = CFUNCTYPE(c_int, POINTER(Display), GLuint, GLuint) 	# GL/glxext.h:705
PFNGLXQUERYSWAPGROUPNVPROC = CFUNCTYPE(c_int, POINTER(Display), GLXDrawable, POINTER(GLuint), POINTER(GLuint)) 	# GL/glxext.h:709
PFNGLXQUERYMAXSWAPGROUPSNVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, POINTER(GLuint), POINTER(GLuint)) 	# GL/glxext.h:714
PFNGLXQUERYFRAMECOUNTNVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, POINTER(GLuint)) 	# GL/glxext.h:719
PFNGLXRESETFRAMECOUNTNVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int) 	# GL/glxext.h:723
# NV_video_out (GL/glxext.h:726)
GLX_NV_video_out = 1 	# GL/glxext.h:727
# GL/glxext.h:729
glXGetVideoDeviceNV = _link_function('glXGetVideoDeviceNV', c_int, [POINTER(Display), c_int, c_int, POINTER(GLXVideoDeviceNV)], 'NV_video_out')

# GL/glxext.h:732
glXReleaseVideoDeviceNV = _link_function('glXReleaseVideoDeviceNV', c_int, [POINTER(Display), c_int, GLXVideoDeviceNV], 'NV_video_out')

GLXPbuffer = pyglet.gl.glx.GLXPbuffer
# GL/glxext.h:735
glXBindVideoImageNV = _link_function('glXBindVideoImageNV', c_int, [POINTER(Display), GLXVideoDeviceNV, GLXPbuffer, c_int], 'NV_video_out')

# GL/glxext.h:738
glXReleaseVideoImageNV = _link_function('glXReleaseVideoImageNV', c_int, [POINTER(Display), GLXPbuffer], 'NV_video_out')

GLboolean = c_ubyte 	# /usr/include/GL/gl.h:154
# GL/glxext.h:740
glXSendPbufferToVideoNV = _link_function('glXSendPbufferToVideoNV', c_int, [POINTER(Display), GLXPbuffer, c_int, POINTER(c_ulong), GLboolean], 'NV_video_out')

# GL/glxext.h:745
glXGetVideoInfoNV = _link_function('glXGetVideoInfoNV', c_int, [POINTER(Display), c_int, GLXVideoDeviceNV, POINTER(c_ulong), POINTER(c_ulong)], 'NV_video_out')

PFNGLXGETVIDEODEVICENVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, c_int, POINTER(GLXVideoDeviceNV)) 	# GL/glxext.h:750
PFNGLXRELEASEVIDEODEVICENVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, GLXVideoDeviceNV) 	# GL/glxext.h:755
PFNGLXBINDVIDEOIMAGENVPROC = CFUNCTYPE(c_int, POINTER(Display), GLXVideoDeviceNV, GLXPbuffer, c_int) 	# GL/glxext.h:759
PFNGLXRELEASEVIDEOIMAGENVPROC = CFUNCTYPE(c_int, POINTER(Display), GLXPbuffer) 	# GL/glxext.h:764
PFNGLXSENDPBUFFERTOVIDEONVPROC = CFUNCTYPE(c_int, POINTER(Display), GLXPbuffer, c_int, POINTER(c_ulong), GLboolean) 	# GL/glxext.h:767
PFNGLXGETVIDEOINFONVPROC = CFUNCTYPE(c_int, POINTER(Display), c_int, GLXVideoDeviceNV, POINTER(c_ulong), POINTER(c_ulong)) 	# GL/glxext.h:773
# EXT_texture_from_pixmap (GL/glxext.h:779)
# GL/glxext.h:782
glXBindTexImageEXT = _link_function('glXBindTexImageEXT', None, [POINTER(Display), GLXDrawable, c_int, POINTER(c_int)], 'EXT_texture_from_pixmap')

# GL/glxext.h:784
glXReleaseTextImageEXT = _link_function('glXReleaseTextImageEXT', None, [POINTER(Display), GLXDrawable, c_int], 'EXT_texture_from_pixmap')

PFNGLXBINDTEXIMAGEEXTPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, c_int, POINTER(c_int)) 	# GL/glxext.h:787
PFNGLXRELEASETEXIMAGEEXTPROC = CFUNCTYPE(None, POINTER(Display), GLXDrawable, c_int) 	# GL/glxext.h:791
# NV_vertex_array_range (/usr/include/GL/glx.h:349)
# MESA_allocate_memory (/usr/include/GL/glx.h:363)
# ARB_render_texture (/usr/include/GL/glx.h:380)
# NV_float_buffer (/usr/include/GL/glx.h:393)
# MESA_swap_frame_usage (/usr/include/GL/glx.h:405)
# MESA_swap_control (/usr/include/GL/glx.h:425)
# EXT_texture_from_pixmap (/usr/include/GL/glx.h:442)

__all__ = ['GLX_GLXEXT_VERSION', 'GLX_SAMPLE_BUFFERS_ARB', 'GLX_SAMPLES_ARB',
'GLX_RGBA_FLOAT_TYPE_ARB', 'GLX_RGBA_FLOAT_BIT_ARB',
'GLX_SAMPLE_BUFFERS_SGIS', 'GLX_SAMPLES_SGIS', 'GLX_X_VISUAL_TYPE_EXT',
'GLX_TRANSPARENT_TYPE_EXT', 'GLX_TRANSPARENT_INDEX_VALUE_EXT',
'GLX_TRANSPARENT_RED_VALUE_EXT', 'GLX_TRANSPARENT_GREEN_VALUE_EXT',
'GLX_TRANSPARENT_BLUE_VALUE_EXT', 'GLX_TRANSPARENT_ALPHA_VALUE_EXT',
'GLX_NONE_EXT', 'GLX_TRUE_COLOR_EXT', 'GLX_DIRECT_COLOR_EXT',
'GLX_PSEUDO_COLOR_EXT', 'GLX_STATIC_COLOR_EXT', 'GLX_GRAY_SCALE_EXT',
'GLX_STATIC_GRAY_EXT', 'GLX_TRANSPARENT_RGB_EXT', 'GLX_TRANSPARENT_INDEX_EXT',
'GLX_VISUAL_CAVEAT_EXT', 'GLX_SLOW_VISUAL_EXT',
'GLX_NON_CONFORMANT_VISUAL_EXT', 'GLX_SHARE_CONTEXT_EXT', 'GLX_VISUAL_ID_EXT',
'GLX_SCREEN_EXT', 'GLX_WINDOW_BIT_SGIX', 'GLX_PIXMAP_BIT_SGIX',
'GLX_RGBA_BIT_SGIX', 'GLX_COLOR_INDEX_BIT_SGIX', 'GLX_DRAWABLE_TYPE_SGIX',
'GLX_RENDER_TYPE_SGIX', 'GLX_X_RENDERABLE_SGIX', 'GLX_FBCONFIG_ID_SGIX',
'GLX_RGBA_TYPE_SGIX', 'GLX_COLOR_INDEX_TYPE_SGIX', 'GLX_PBUFFER_BIT_SGIX',
'GLX_BUFFER_CLOBBER_MASK_SGIX', 'GLX_FRONT_LEFT_BUFFER_BIT_SGIX',
'GLX_FRONT_RIGHT_BUFFER_BIT_SGIX', 'GLX_BACK_LEFT_BUFFER_BIT_SGIX',
'GLX_BACK_RIGHT_BUFFER_BIT_SGIX', 'GLX_AUX_BUFFERS_BIT_SGIX',
'GLX_DEPTH_BUFFER_BIT_SGIX', 'GLX_STENCIL_BUFFER_BIT_SGIX',
'GLX_ACCUM_BUFFER_BIT_SGIX', 'GLX_SAMPLE_BUFFERS_BIT_SGIX',
'GLX_MAX_PBUFFER_WIDTH_SGIX', 'GLX_MAX_PBUFFER_HEIGHT_SGIX',
'GLX_MAX_PBUFFER_PIXELS_SGIX', 'GLX_OPTIMAL_PBUFFER_WIDTH_SGIX',
'GLX_OPTIMAL_PBUFFER_HEIGHT_SGIX', 'GLX_PRESERVED_CONTENTS_SGIX',
'GLX_LARGEST_PBUFFER_SGIX', 'GLX_WIDTH_SGIX', 'GLX_HEIGHT_SGIX',
'GLX_EVENT_MASK_SGIX', 'GLX_DAMAGED_SGIX', 'GLX_SAVED_SGIX',
'GLX_WINDOW_SGIX', 'GLX_PBUFFER_SGIX', 'GLX_SYNC_FRAME_SGIX',
'GLX_SYNC_SWAP_SGIX', 'GLX_DIGITAL_MEDIA_PBUFFER_SGIX',
'GLX_BLENDED_RGBA_SGIS', 'GLX_MULTISAMPLE_SUB_RECT_WIDTH_SGIS',
'GLX_MULTISAMPLE_SUB_RECT_HEIGHT_SGIS', 'GLX_SAMPLE_BUFFERS_3DFX',
'GLX_SAMPLES_3DFX', 'GLX_3DFX_WINDOW_MODE_MESA',
'GLX_3DFX_FULLSCREEN_MODE_MESA', 'GLX_VISUAL_SELECT_GROUP_SGIX',
'GLX_SWAP_METHOD_OML', 'GLX_SWAP_EXCHANGE_OML', 'GLX_SWAP_COPY_OML',
'GLX_SWAP_UNDEFINED_OML', 'GLX_FLOAT_COMPONENTS_NV',
'GLX_HYPERPIPE_PIPE_NAME_LENGTH_SGIX', 'GLX_BAD_HYPERPIPE_CONFIG_SGIX',
'GLX_BAD_HYPERPIPE_SGIX', 'GLX_HYPERPIPE_DISPLAY_PIPE_SGIX',
'GLX_HYPERPIPE_RENDER_PIPE_SGIX', 'GLX_PIPE_RECT_SGIX',
'GLX_PIPE_RECT_LIMITS_SGIX', 'GLX_HYPERPIPE_STEREO_SGIX',
'GLX_HYPERPIPE_PIXEL_AVERAGE_SGIX', 'GLX_HYPERPIPE_ID_SGIX',
'GLXVideoSourceSGIX', 'GLXFBConfigIDSGIX', 'GLXFBConfigSGIX',
'GLXPbufferSGIX', 'GLXBufferClobberEventSGIX', 'GLXVideoDeviceNV',
'GLX_VIDEO_OUT_COLOR_NV', 'GLX_VIDEO_OUT_ALPHA_NV', 'GLX_VIDEO_OUT_DEPTH_NV',
'GLX_VIDEO_OUT_COLOR_AND_ALPHA_NV', 'GLX_VIDEO_OUT_COLOR_AND_DEPTH_NV',
'GLX_VIDEO_OUT_FRAME_NV', 'GLX_VIDEO_OUT_FIELD_1_NV',
'GLX_VIDEO_OUT_FIELD_2_NV', 'GLX_BIND_TO_TEXTURE_RGB_EXT',
'GLX_BIND_TO_TEXTURE_RGBA_EXT', 'GLX_BIND_TO_MIPMAP_TEXTURE_EXT',
'GLX_BIND_TO_TEXTURE_TARGETS_EXT', 'GLX_Y_INVERTED_EXT',
'GLX_TEXTURE_FORMAT_EXT', 'GLX_TEXTURE_TARGET_EXT', 'GLX_MIPMAP_TEXTURE_EXT',
'GLX_TEXTURE_FORMAT_NONE_EXT', 'GLX_TEXTURE_FORMAT_RGB_EXT',
'GLX_TEXTURE_FORMAT_RGBA_EXT', 'GLX_TEXTURE_1D_BIT_EXT',
'GLX_TEXTURE_2D_BIT_EXT', 'GLX_TEXTURE_RECTANGLE_BIT_EXT',
'GLX_TEXTURE_1D_EXT', 'GLX_TEXTURE_2D_EXT', 'GLX_TEXTURE_RECTANGLE_EXT',
'GLX_FRONT_LEFT_EXT', 'GLX_FRONT_RIGHT_EXT', 'GLX_BACK_LEFT_EXT',
'GLX_BACK_RIGHT_EXT', 'GLX_FRONT_EXT', 'GLX_BACK_EXT', 'GLX_AUX0_EXT',
'GLX_AUX1_EXT', 'GLX_AUX2_EXT', 'GLX_AUX3_EXT', 'GLX_AUX4_EXT',
'GLX_AUX5_EXT', 'GLX_AUX6_EXT', 'GLX_AUX7_EXT', 'GLX_AUX8_EXT',
'GLX_AUX9_EXT', 'GLX_ARB_multisample', 'GLX_ARB_fbconfig_float',
'GLX_SGIS_multisample', 'GLX_EXT_visual_info', 'GLX_SGI_swap_control',
'glXSwapIntervalSGI', 'PFNGLXSWAPINTERVALSGIPROC', 'GLX_SGI_video_sync',
'glXGetVideoSyncSGI', 'glXWaitVideoSyncSGI', 'glXGetRefreshRateSGI',
'PFNGLXGETVIDEOSYNCSGIPROC', 'PFNGLXWAITVIDEOSYNCSGIPROC',
'PFNGLXGETREFRESHRATESGIPROC', 'GLX_SGI_make_current_read',
'glXMakeCurrentReadSGI', 'glXGetCurrentReadDrawableSGI',
'PFNGLXMAKECURRENTREADSGIPROC', 'PFNGLXGETCURRENTREADDRAWABLESGIPROC',
'GLX_SGIX_video_source', 'GLX_EXT_visual_rating', 'GLX_EXT_import_context',
'glXGetCurrentDisplayEXT', 'glXQueryContextInfoEXT', 'glXGetContextIDEXT',
'glXImportContextEXT', 'glXFreeContextEXT', 'PFNGLXGETCURRENTDISPLAYEXTPROC',
'PFNGLXQUERYCONTEXTINFOEXTPROC', 'PFNGLXGETCONTEXTIDEXTPROC',
'PFNGLXIMPORTCONTEXTEXTPROC', 'PFNGLXFREECONTEXTEXTPROC', 'GLX_SGIX_fbconfig',
'glXGetFBConfigAttribSGIX', 'glXChooseFBConfigSGIX',
'glXCreateGLXPixmapWithConfigSGIX', 'glXCreateContextWithConfigSGIX',
'glXGetVisualFromFBConfigSGIX', 'glXGetFBConfigFromVisualSGIX',
'PFNGLXGETFBCONFIGATTRIBSGIXPROC', 'PFNGLXCHOOSEFBCONFIGSGIXPROC',
'PFNGLXCREATEGLXPIXMAPWITHCONFIGSGIXPROC',
'PFNGLXCREATECONTEXTWITHCONFIGSGIXPROC',
'PFNGLXGETVISUALFROMFBCONFIGSGIXPROC', 'PFNGLXGETFBCONFIGFROMVISUALSGIXPROC',
'GLX_SGIX_pbuffer', 'glXCreateGLXPbufferSGIX', 'glXDestroyGLXPbufferSGIX',
'glXQueryGLXPbufferSGIX', 'glXSelectEventSGIX', 'glXGetSelectedEventSGIX',
'PFNGLXCREATEGLXPBUFFERSGIXPROC', 'PFNGLXDESTROYGLXPBUFFERSGIXPROC',
'PFNGLXQUERYGLXPBUFFERSGIXPROC', 'PFNGLXSELECTEVENTSGIXPROC',
'PFNGLXGETSELECTEDEVENTSGIXPROC', 'GLX_SGI_cushion', 'glXCushionSGI',
'PFNGLXCUSHIONSGIPROC', 'GLX_SGIX_video_resize', 'glXBindChannelToWindowSGIX',
'glXChannelRectSGIX', 'glXQueryChannelRectSGIX', 'glXQueryChannelDeltasSGIX',
'glXChannelRectSyncSGIX', 'PFNGLXBINDCHANNELTOWINDOWSGIXPROC',
'PFNGLXCHANNELRECTSGIXPROC', 'PFNGLXQUERYCHANNELRECTSGIXPROC',
'PFNGLXQUERYCHANNELDELTASSGIXPROC', 'PFNGLXCHANNELRECTSYNCSGIXPROC',
'GLX_SGIX_dmbuffer', 'GLX_SGIX_swap_group', 'glXJoinSwapGroupSGIX',
'PFNGLXJOINSWAPGROUPSGIXPROC', 'GLX_SGIX_swap_barrier',
'glXBindSwapBarrierSGIX', 'glXQueryMaxSwapBarriersSGIX',
'PFNGLXBINDSWAPBARRIERSGIXPROC', 'PFNGLXQUERYMAXSWAPBARRIERSSGIXPROC',
'GLX_SUN_get_transparent_index', 'glXGetTransparentIndexSUN',
'PFNGLXGETTRANSPARENTINDEXSUNPROC', 'GLX_MESA_copy_sub_buffer',
'glXCopySubBufferMESA', 'PFNGLXCOPYSUBBUFFERMESAPROC',
'GLX_MESA_pixmap_colormap', 'glXCreateGLXPixmapMESA',
'PFNGLXCREATEGLXPIXMAPMESAPROC', 'GLX_MESA_release_buffers',
'glXReleaseBuffersMESA', 'PFNGLXRELEASEBUFFERSMESAPROC',
'GLX_MESA_set_3dfx_mode', 'glXSet3DfxModeMESA', 'PFNGLXSET3DFXMODEMESAPROC',
'GLX_SGIX_visual_select_group', 'GLX_OML_swap_method', 'GLX_OML_sync_control',
'glXGetSyncValuesOML', 'glXGetMscRateOML', 'glXSwapBuffersMscOML',
'glXWaitForMscOML', 'glXWaitForSbcOML', 'PFNGLXGETSYNCVALUESOMLPROC',
'PFNGLXGETMSCRATEOMLPROC', 'PFNGLXSWAPBUFFERSMSCOMLPROC',
'PFNGLXWAITFORMSCOMLPROC', 'PFNGLXWAITFORSBCOMLPROC', 'GLX_NV_float_buffer',
'GLX_SGIX_hyperpipe', 'GLXHyperpipeNetworkSGIX', 'GLXHyperpipeConfigSGIX',
'GLXPipeRect', 'GLXPipeRectLimits', 'glXQueryHyperpipeNetworkSGIX',
'glXHyperpipeConfigSGIX', 'glXQueryHyperpipeConfigSGIX',
'glXDestroyHyperpipeConfigSGIX', 'glXBindHyperpipeSGIX',
'glXQueryHyperpipeBestAttribSGIX', 'glXHyperpipeAttribSGIX',
'glXQueryHyperpipeAttribSGIX', 'PFNGLXQUERYHYPERPIPENETWORKSGIXPROC',
'PFNGLXHYPERPIPECONFIGSGIXPROC', 'PFNGLXQUERYHYPERPIPECONFIGSGIXPROC',
'PFNGLXDESTROYHYPERPIPECONFIGSGIXPROC', 'PFNGLXBINDHYPERPIPESGIXPROC',
'PFNGLXQUERYHYPERPIPEBESTATTRIBSGIXPROC', 'PFNGLXHYPERPIPEATTRIBSGIXPROC',
'PFNGLXQUERYHYPERPIPEATTRIBSGIXPROC', 'GLX_MESA_agp_offset',
'glXGetAGPOffsetMESA', 'PFNGLXGETAGPOFFSETMESAPROC',
'GLX_NV_vertex_array_range', 'glXAllocateMemoryNV', 'glXFreeMemoryNV',
'PFNGLXALLOCATEMEMORYNVPROC', 'PFNGLXFREEMEMORYNVPROC', 'GLX_NV_swap_group',
'glXJoinSwapGroupNV', 'glXBindSwapBarrierNV', 'glXQuerySwapGroupNV',
'glXQueryMaxSwapGroupsNV', 'glXQueryFrameCountNV', 'glXResetFrameCountNV',
'PFNGLXJOINSWAPGROUPNVPROC', 'PFNGLXBINDSWAPBARRIERNVPROC',
'PFNGLXQUERYSWAPGROUPNVPROC', 'PFNGLXQUERYMAXSWAPGROUPSNVPROC',
'PFNGLXQUERYFRAMECOUNTNVPROC', 'PFNGLXRESETFRAMECOUNTNVPROC',
'GLX_NV_video_out', 'glXGetVideoDeviceNV', 'glXReleaseVideoDeviceNV',
'glXBindVideoImageNV', 'glXReleaseVideoImageNV', 'glXSendPbufferToVideoNV',
'glXGetVideoInfoNV', 'PFNGLXGETVIDEODEVICENVPROC',
'PFNGLXRELEASEVIDEODEVICENVPROC', 'PFNGLXBINDVIDEOIMAGENVPROC',
'PFNGLXRELEASEVIDEOIMAGENVPROC', 'PFNGLXSENDPBUFFERTOVIDEONVPROC',
'PFNGLXGETVIDEOINFONVPROC', 'glXBindTexImageEXT', 'glXReleaseTextImageEXT',
'PFNGLXBINDTEXIMAGEEXTPROC', 'PFNGLXRELEASETEXIMAGEEXTPROC']
# END GENERATED CONTENT (do not edit above this line)









