#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包AI配置验证脚本
用于验证配置文件和代码修改是否正确
"""

import json
import os

def verify_config():
    """验证config.json配置"""
    config_path = "config.json"
    
    if not os.path.exists(config_path):
        print("❌ config.json文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📋 当前配置验证:")
        print(f"  AI类型: {config.get('now_AI_type', '未设置')}")
        print(f"  自动发送: {config.get('submit_text', '未设置')}")
        print(f"  豆包AI URL: {config.get('ChatGPT_URL', {}).get('Doubao_AI', '未设置')}")
        
        # 检查关键配置
        issues = []
        
        if config.get('now_AI_type') != 'Doubao_AI':
            issues.append(f"AI类型应该是 'Doubao_AI'，当前是 '{config.get('now_AI_type')}'")
        
        if config.get('submit_text') != True:
            issues.append(f"自动发送应该是 true，当前是 {config.get('submit_text')}")
        
        doubao_url = config.get('ChatGPT_URL', {}).get('Doubao_AI')
        expected_url = "https://www.doubao.com/chat/"
        if doubao_url != expected_url:
            issues.append(f"豆包AI URL应该是 '{expected_url}'，当前是 '{doubao_url}'")
        
        if issues:
            print("\n⚠️  发现配置问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("\n✅ 配置验证通过!")
            return True
            
    except json.JSONDecodeError as e:
        print(f"❌ config.json格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def verify_code_changes():
    """验证代码修改"""
    dock_file = "dock_web_view.py"
    
    if not os.path.exists(dock_file):
        print("❌ dock_web_view.py文件不存在")
        return False
    
    try:
        with open(dock_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n🔍 代码修改验证:")
        
        # 检查关键修改
        checks = [
            ("豆包AI输入框选择器", ".composer-input" in content),
            ("豆包AI发送按钮选择器", ".chat-send-button" in content),
            ("调试DOM结构函数", "debugDOMStructure" in content),
            ("全局调试函数", "window.debugDoubaoAI" in content),
            ("豆包AI延迟配置", "豆包AI需要更长的延迟" in content),
            ("多种按钮查找策略", "通过文本找到发送按钮" in content),
        ]
        
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("\n✅ 代码修改验证通过!")
        else:
            print("\n⚠️  部分代码修改可能有问题")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 读取代码文件失败: {e}")
        return False

def generate_test_instructions():
    """生成测试说明"""
    print("\n📝 测试步骤:")
    print("1. 重启Anki应用程序")
    print("2. 确认插件界面显示豆包AI图标")
    print("3. 在Anki中选择一些文本")
    print("4. 右键选择 '🤖Explain with AnkiTerminator'")
    print("5. 观察豆包AI是否自动填入文本并发送")
    print("6. 如果没有自动发送，按F12打开开发者工具查看控制台")
    
    print("\n🔧 调试方法:")
    print("1. 在豆包AI页面控制台运行: window.debugDoubaoAI()")
    print("2. 查看输出的DOM结构信息")
    print("3. 根据实际DOM结构调整选择器")
    
    print("\n📞 如果仍有问题:")
    print("1. 检查豆包AI网站是否有更新")
    print("2. 运行 test_doubao_debug.py 获取详细调试信息")
    print("3. 根据调试结果更新选择器配置")

def main():
    print("🔍 豆包AI配置验证工具")
    print("=" * 50)
    
    config_ok = verify_config()
    code_ok = verify_code_changes()
    
    print("\n" + "=" * 50)
    if config_ok and code_ok:
        print("🎉 所有验证通过！可以开始测试豆包AI自动发送功能")
    else:
        print("⚠️  发现问题，请检查上述提示并修复")
    
    generate_test_instructions()

if __name__ == "__main__":
    main()
