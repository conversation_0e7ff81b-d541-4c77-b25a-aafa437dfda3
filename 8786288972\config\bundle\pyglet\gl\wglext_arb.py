"""Wrapper for http://oss.sgi.com/projects/ogl-sample/ABI/wglext.h

Generated by tools/gengl.py.
Do not modify this file.
"""
from __future__ import annotations

from ctypes import CFUNCTYPE, POINTER, Structure, c_char_p, c_float, c_int, c_long, c_ubyte, c_uint, c_ulong, c_ushort
from ctypes.wintypes import HANDLE, CHAR, DWORD, LONG, HDC, UINT, USHORT, LPVOID, FLOAT

from pyglet.gl.lib import c_void
from pyglet.gl.lib import link_WGL as _link_function
from pyglet.libs.win32.types import INT64, INT32

# BEGIN GENERATED CONTENT (do not edit below this line)

# This content is generated by tools/gengl.py.
# Wrapper for http://www.opengl.org/registry/api/wglext.h


# H (C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:7)
# H (C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:7)
WIN32_LEAN_AND_MEAN = 1 	# http://www.opengl.org/registry/api/wglext.h:34
WGL_WGLEXT_VERSION = 11 	# http://www.opengl.org/registry/api/wglext.h:53
# ARB_buffer_region (http://www.opengl.org/registry/api/wglext.h:55)
WGL_FRONT_COLOR_BUFFER_BIT_ARB = 1 	# http://www.opengl.org/registry/api/wglext.h:56
WGL_BACK_COLOR_BUFFER_BIT_ARB = 2 	# http://www.opengl.org/registry/api/wglext.h:57
WGL_DEPTH_BUFFER_BIT_ARB = 4 	# http://www.opengl.org/registry/api/wglext.h:58
WGL_STENCIL_BUFFER_BIT_ARB = 8 	# http://www.opengl.org/registry/api/wglext.h:59
# ARB_multisample (http://www.opengl.org/registry/api/wglext.h:62)
WGL_SAMPLE_BUFFERS_ARB = 8257 	# http://www.opengl.org/registry/api/wglext.h:63
WGL_SAMPLES_ARB = 8258 	# http://www.opengl.org/registry/api/wglext.h:64
# ARB_extensions_string (http://www.opengl.org/registry/api/wglext.h:67)
# ARB_pixel_format (http://www.opengl.org/registry/api/wglext.h:70)
WGL_NUMBER_PIXEL_FORMATS_ARB = 8192 	# http://www.opengl.org/registry/api/wglext.h:71
WGL_DRAW_TO_WINDOW_ARB = 8193 	# http://www.opengl.org/registry/api/wglext.h:72
WGL_DRAW_TO_BITMAP_ARB = 8194 	# http://www.opengl.org/registry/api/wglext.h:73
WGL_ACCELERATION_ARB = 8195 	# http://www.opengl.org/registry/api/wglext.h:74
WGL_NEED_PALETTE_ARB = 8196 	# http://www.opengl.org/registry/api/wglext.h:75
WGL_NEED_SYSTEM_PALETTE_ARB = 8197 	# http://www.opengl.org/registry/api/wglext.h:76
WGL_SWAP_LAYER_BUFFERS_ARB = 8198 	# http://www.opengl.org/registry/api/wglext.h:77
WGL_SWAP_METHOD_ARB = 8199 	# http://www.opengl.org/registry/api/wglext.h:78
WGL_NUMBER_OVERLAYS_ARB = 8200 	# http://www.opengl.org/registry/api/wglext.h:79
WGL_NUMBER_UNDERLAYS_ARB = 8201 	# http://www.opengl.org/registry/api/wglext.h:80
WGL_TRANSPARENT_ARB = 8202 	# http://www.opengl.org/registry/api/wglext.h:81
WGL_TRANSPARENT_RED_VALUE_ARB = 8247 	# http://www.opengl.org/registry/api/wglext.h:82
WGL_TRANSPARENT_GREEN_VALUE_ARB = 8248 	# http://www.opengl.org/registry/api/wglext.h:83
WGL_TRANSPARENT_BLUE_VALUE_ARB = 8249 	# http://www.opengl.org/registry/api/wglext.h:84
WGL_TRANSPARENT_ALPHA_VALUE_ARB = 8250 	# http://www.opengl.org/registry/api/wglext.h:85
WGL_TRANSPARENT_INDEX_VALUE_ARB = 8251 	# http://www.opengl.org/registry/api/wglext.h:86
WGL_SHARE_DEPTH_ARB = 8204 	# http://www.opengl.org/registry/api/wglext.h:87
WGL_SHARE_STENCIL_ARB = 8205 	# http://www.opengl.org/registry/api/wglext.h:88
WGL_SHARE_ACCUM_ARB = 8206 	# http://www.opengl.org/registry/api/wglext.h:89
WGL_SUPPORT_GDI_ARB = 8207 	# http://www.opengl.org/registry/api/wglext.h:90
WGL_SUPPORT_OPENGL_ARB = 8208 	# http://www.opengl.org/registry/api/wglext.h:91
WGL_DOUBLE_BUFFER_ARB = 8209 	# http://www.opengl.org/registry/api/wglext.h:92
WGL_STEREO_ARB = 8210 	# http://www.opengl.org/registry/api/wglext.h:93
WGL_PIXEL_TYPE_ARB = 8211 	# http://www.opengl.org/registry/api/wglext.h:94
WGL_COLOR_BITS_ARB = 8212 	# http://www.opengl.org/registry/api/wglext.h:95
WGL_RED_BITS_ARB = 8213 	# http://www.opengl.org/registry/api/wglext.h:96
WGL_RED_SHIFT_ARB = 8214 	# http://www.opengl.org/registry/api/wglext.h:97
WGL_GREEN_BITS_ARB = 8215 	# http://www.opengl.org/registry/api/wglext.h:98
WGL_GREEN_SHIFT_ARB = 8216 	# http://www.opengl.org/registry/api/wglext.h:99
WGL_BLUE_BITS_ARB = 8217 	# http://www.opengl.org/registry/api/wglext.h:100
WGL_BLUE_SHIFT_ARB = 8218 	# http://www.opengl.org/registry/api/wglext.h:101
WGL_ALPHA_BITS_ARB = 8219 	# http://www.opengl.org/registry/api/wglext.h:102
WGL_ALPHA_SHIFT_ARB = 8220 	# http://www.opengl.org/registry/api/wglext.h:103
WGL_ACCUM_BITS_ARB = 8221 	# http://www.opengl.org/registry/api/wglext.h:104
WGL_ACCUM_RED_BITS_ARB = 8222 	# http://www.opengl.org/registry/api/wglext.h:105
WGL_ACCUM_GREEN_BITS_ARB = 8223 	# http://www.opengl.org/registry/api/wglext.h:106
WGL_ACCUM_BLUE_BITS_ARB = 8224 	# http://www.opengl.org/registry/api/wglext.h:107
WGL_ACCUM_ALPHA_BITS_ARB = 8225 	# http://www.opengl.org/registry/api/wglext.h:108
WGL_DEPTH_BITS_ARB = 8226 	# http://www.opengl.org/registry/api/wglext.h:109
WGL_STENCIL_BITS_ARB = 8227 	# http://www.opengl.org/registry/api/wglext.h:110
WGL_AUX_BUFFERS_ARB = 8228 	# http://www.opengl.org/registry/api/wglext.h:111
WGL_NO_ACCELERATION_ARB = 8229 	# http://www.opengl.org/registry/api/wglext.h:112
WGL_GENERIC_ACCELERATION_ARB = 8230 	# http://www.opengl.org/registry/api/wglext.h:113
WGL_FULL_ACCELERATION_ARB = 8231 	# http://www.opengl.org/registry/api/wglext.h:114
WGL_SWAP_EXCHANGE_ARB = 8232 	# http://www.opengl.org/registry/api/wglext.h:115
WGL_SWAP_COPY_ARB = 8233 	# http://www.opengl.org/registry/api/wglext.h:116
WGL_SWAP_UNDEFINED_ARB = 8234 	# http://www.opengl.org/registry/api/wglext.h:117
WGL_TYPE_RGBA_ARB = 8235 	# http://www.opengl.org/registry/api/wglext.h:118
WGL_TYPE_COLORINDEX_ARB = 8236 	# http://www.opengl.org/registry/api/wglext.h:119
# ARB_make_current_read (http://www.opengl.org/registry/api/wglext.h:122)
ERROR_INVALID_PIXEL_TYPE_ARB = 8259 	# http://www.opengl.org/registry/api/wglext.h:123
ERROR_INCOMPATIBLE_DEVICE_CONTEXTS_ARB = 8276 	# http://www.opengl.org/registry/api/wglext.h:124
# ARB_pbuffer (http://www.opengl.org/registry/api/wglext.h:127)
WGL_DRAW_TO_PBUFFER_ARB = 8237 	# http://www.opengl.org/registry/api/wglext.h:128
WGL_MAX_PBUFFER_PIXELS_ARB = 8238 	# http://www.opengl.org/registry/api/wglext.h:129
WGL_MAX_PBUFFER_WIDTH_ARB = 8239 	# http://www.opengl.org/registry/api/wglext.h:130
WGL_MAX_PBUFFER_HEIGHT_ARB = 8240 	# http://www.opengl.org/registry/api/wglext.h:131
WGL_PBUFFER_LARGEST_ARB = 8243 	# http://www.opengl.org/registry/api/wglext.h:132
WGL_PBUFFER_WIDTH_ARB = 8244 	# http://www.opengl.org/registry/api/wglext.h:133
WGL_PBUFFER_HEIGHT_ARB = 8245 	# http://www.opengl.org/registry/api/wglext.h:134
WGL_PBUFFER_LOST_ARB = 8246 	# http://www.opengl.org/registry/api/wglext.h:135
# ARB_render_texture (http://www.opengl.org/registry/api/wglext.h:138)
WGL_BIND_TO_TEXTURE_RGB_ARB = 8304 	# http://www.opengl.org/registry/api/wglext.h:139
WGL_BIND_TO_TEXTURE_RGBA_ARB = 8305 	# http://www.opengl.org/registry/api/wglext.h:140
WGL_TEXTURE_FORMAT_ARB = 8306 	# http://www.opengl.org/registry/api/wglext.h:141
WGL_TEXTURE_TARGET_ARB = 8307 	# http://www.opengl.org/registry/api/wglext.h:142
WGL_MIPMAP_TEXTURE_ARB = 8308 	# http://www.opengl.org/registry/api/wglext.h:143
WGL_TEXTURE_RGB_ARB = 8309 	# http://www.opengl.org/registry/api/wglext.h:144
WGL_TEXTURE_RGBA_ARB = 8310 	# http://www.opengl.org/registry/api/wglext.h:145
WGL_NO_TEXTURE_ARB = 8311 	# http://www.opengl.org/registry/api/wglext.h:146
WGL_TEXTURE_CUBE_MAP_ARB = 8312 	# http://www.opengl.org/registry/api/wglext.h:147
WGL_TEXTURE_1D_ARB = 8313 	# http://www.opengl.org/registry/api/wglext.h:148
WGL_TEXTURE_2D_ARB = 8314 	# http://www.opengl.org/registry/api/wglext.h:149
WGL_MIPMAP_LEVEL_ARB = 8315 	# http://www.opengl.org/registry/api/wglext.h:150
WGL_CUBE_MAP_FACE_ARB = 8316 	# http://www.opengl.org/registry/api/wglext.h:151
WGL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB = 8317 	# http://www.opengl.org/registry/api/wglext.h:152
WGL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB = 8318 	# http://www.opengl.org/registry/api/wglext.h:153
WGL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB = 8319 	# http://www.opengl.org/registry/api/wglext.h:154
WGL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB = 8320 	# http://www.opengl.org/registry/api/wglext.h:155
WGL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB = 8321 	# http://www.opengl.org/registry/api/wglext.h:156
WGL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB = 8322 	# http://www.opengl.org/registry/api/wglext.h:157
WGL_FRONT_LEFT_ARB = 8323 	# http://www.opengl.org/registry/api/wglext.h:158
WGL_FRONT_RIGHT_ARB = 8324 	# http://www.opengl.org/registry/api/wglext.h:159
WGL_BACK_LEFT_ARB = 8325 	# http://www.opengl.org/registry/api/wglext.h:160
WGL_BACK_RIGHT_ARB = 8326 	# http://www.opengl.org/registry/api/wglext.h:161
WGL_AUX0_ARB = 8327 	# http://www.opengl.org/registry/api/wglext.h:162
WGL_AUX1_ARB = 8328 	# http://www.opengl.org/registry/api/wglext.h:163
WGL_AUX2_ARB = 8329 	# http://www.opengl.org/registry/api/wglext.h:164
WGL_AUX3_ARB = 8330 	# http://www.opengl.org/registry/api/wglext.h:165
WGL_AUX4_ARB = 8331 	# http://www.opengl.org/registry/api/wglext.h:166
WGL_AUX5_ARB = 8332 	# http://www.opengl.org/registry/api/wglext.h:167
WGL_AUX6_ARB = 8333 	# http://www.opengl.org/registry/api/wglext.h:168
WGL_AUX7_ARB = 8334 	# http://www.opengl.org/registry/api/wglext.h:169
WGL_AUX8_ARB = 8335 	# http://www.opengl.org/registry/api/wglext.h:170
WGL_AUX9_ARB = 8336 	# http://www.opengl.org/registry/api/wglext.h:171
# ARB_pixel_format_float (http://www.opengl.org/registry/api/wglext.h:174)
WGL_TYPE_RGBA_FLOAT_ARB = 8608 	# http://www.opengl.org/registry/api/wglext.h:175
# ARB_create_context (http://www.opengl.org/registry/api/wglext.h:178)
WGL_CONTEXT_DEBUG_BIT_ARB = 1 	# http://www.opengl.org/registry/api/wglext.h:179
WGL_CONTEXT_FORWARD_COMPATIBLE_BIT_ARB = 2 	# http://www.opengl.org/registry/api/wglext.h:180
WGL_CONTEXT_MAJOR_VERSION_ARB = 8337 	# http://www.opengl.org/registry/api/wglext.h:181
WGL_CONTEXT_MINOR_VERSION_ARB = 8338 	# http://www.opengl.org/registry/api/wglext.h:182
WGL_CONTEXT_LAYER_PLANE_ARB = 8339 	# http://www.opengl.org/registry/api/wglext.h:183
WGL_CONTEXT_FLAGS_ARB = 8340 	# http://www.opengl.org/registry/api/wglext.h:184
ERROR_INVALID_VERSION_ARB = 8341 	# http://www.opengl.org/registry/api/wglext.h:185
# EXT_make_current_read (http://www.opengl.org/registry/api/wglext.h:188)
ERROR_INVALID_PIXEL_TYPE_EXT = 8259 	# http://www.opengl.org/registry/api/wglext.h:189
# EXT_pixel_format (http://www.opengl.org/registry/api/wglext.h:192)
WGL_NUMBER_PIXEL_FORMATS_EXT = 8192 	# http://www.opengl.org/registry/api/wglext.h:193
WGL_DRAW_TO_WINDOW_EXT = 8193 	# http://www.opengl.org/registry/api/wglext.h:194
WGL_DRAW_TO_BITMAP_EXT = 8194 	# http://www.opengl.org/registry/api/wglext.h:195
WGL_ACCELERATION_EXT = 8195 	# http://www.opengl.org/registry/api/wglext.h:196
WGL_NEED_PALETTE_EXT = 8196 	# http://www.opengl.org/registry/api/wglext.h:197
WGL_NEED_SYSTEM_PALETTE_EXT = 8197 	# http://www.opengl.org/registry/api/wglext.h:198
WGL_SWAP_LAYER_BUFFERS_EXT = 8198 	# http://www.opengl.org/registry/api/wglext.h:199
WGL_SWAP_METHOD_EXT = 8199 	# http://www.opengl.org/registry/api/wglext.h:200
WGL_NUMBER_OVERLAYS_EXT = 8200 	# http://www.opengl.org/registry/api/wglext.h:201
WGL_NUMBER_UNDERLAYS_EXT = 8201 	# http://www.opengl.org/registry/api/wglext.h:202
WGL_TRANSPARENT_EXT = 8202 	# http://www.opengl.org/registry/api/wglext.h:203
WGL_TRANSPARENT_VALUE_EXT = 8203 	# http://www.opengl.org/registry/api/wglext.h:204
WGL_SHARE_DEPTH_EXT = 8204 	# http://www.opengl.org/registry/api/wglext.h:205
WGL_SHARE_STENCIL_EXT = 8205 	# http://www.opengl.org/registry/api/wglext.h:206
WGL_SHARE_ACCUM_EXT = 8206 	# http://www.opengl.org/registry/api/wglext.h:207
WGL_SUPPORT_GDI_EXT = 8207 	# http://www.opengl.org/registry/api/wglext.h:208
WGL_SUPPORT_OPENGL_EXT = 8208 	# http://www.opengl.org/registry/api/wglext.h:209
WGL_DOUBLE_BUFFER_EXT = 8209 	# http://www.opengl.org/registry/api/wglext.h:210
WGL_STEREO_EXT = 8210 	# http://www.opengl.org/registry/api/wglext.h:211
WGL_PIXEL_TYPE_EXT = 8211 	# http://www.opengl.org/registry/api/wglext.h:212
WGL_COLOR_BITS_EXT = 8212 	# http://www.opengl.org/registry/api/wglext.h:213
WGL_RED_BITS_EXT = 8213 	# http://www.opengl.org/registry/api/wglext.h:214
WGL_RED_SHIFT_EXT = 8214 	# http://www.opengl.org/registry/api/wglext.h:215
WGL_GREEN_BITS_EXT = 8215 	# http://www.opengl.org/registry/api/wglext.h:216
WGL_GREEN_SHIFT_EXT = 8216 	# http://www.opengl.org/registry/api/wglext.h:217
WGL_BLUE_BITS_EXT = 8217 	# http://www.opengl.org/registry/api/wglext.h:218
WGL_BLUE_SHIFT_EXT = 8218 	# http://www.opengl.org/registry/api/wglext.h:219
WGL_ALPHA_BITS_EXT = 8219 	# http://www.opengl.org/registry/api/wglext.h:220
WGL_ALPHA_SHIFT_EXT = 8220 	# http://www.opengl.org/registry/api/wglext.h:221
WGL_ACCUM_BITS_EXT = 8221 	# http://www.opengl.org/registry/api/wglext.h:222
WGL_ACCUM_RED_BITS_EXT = 8222 	# http://www.opengl.org/registry/api/wglext.h:223
WGL_ACCUM_GREEN_BITS_EXT = 8223 	# http://www.opengl.org/registry/api/wglext.h:224
WGL_ACCUM_BLUE_BITS_EXT = 8224 	# http://www.opengl.org/registry/api/wglext.h:225
WGL_ACCUM_ALPHA_BITS_EXT = 8225 	# http://www.opengl.org/registry/api/wglext.h:226
WGL_DEPTH_BITS_EXT = 8226 	# http://www.opengl.org/registry/api/wglext.h:227
WGL_STENCIL_BITS_EXT = 8227 	# http://www.opengl.org/registry/api/wglext.h:228
WGL_AUX_BUFFERS_EXT = 8228 	# http://www.opengl.org/registry/api/wglext.h:229
WGL_NO_ACCELERATION_EXT = 8229 	# http://www.opengl.org/registry/api/wglext.h:230
WGL_GENERIC_ACCELERATION_EXT = 8230 	# http://www.opengl.org/registry/api/wglext.h:231
WGL_FULL_ACCELERATION_EXT = 8231 	# http://www.opengl.org/registry/api/wglext.h:232
WGL_SWAP_EXCHANGE_EXT = 8232 	# http://www.opengl.org/registry/api/wglext.h:233
WGL_SWAP_COPY_EXT = 8233 	# http://www.opengl.org/registry/api/wglext.h:234
WGL_SWAP_UNDEFINED_EXT = 8234 	# http://www.opengl.org/registry/api/wglext.h:235
WGL_TYPE_RGBA_EXT = 8235 	# http://www.opengl.org/registry/api/wglext.h:236
WGL_TYPE_COLORINDEX_EXT = 8236 	# http://www.opengl.org/registry/api/wglext.h:237
# EXT_pbuffer (http://www.opengl.org/registry/api/wglext.h:240)
WGL_DRAW_TO_PBUFFER_EXT = 8237 	# http://www.opengl.org/registry/api/wglext.h:241
WGL_MAX_PBUFFER_PIXELS_EXT = 8238 	# http://www.opengl.org/registry/api/wglext.h:242
WGL_MAX_PBUFFER_WIDTH_EXT = 8239 	# http://www.opengl.org/registry/api/wglext.h:243
WGL_MAX_PBUFFER_HEIGHT_EXT = 8240 	# http://www.opengl.org/registry/api/wglext.h:244
WGL_OPTIMAL_PBUFFER_WIDTH_EXT = 8241 	# http://www.opengl.org/registry/api/wglext.h:245
WGL_OPTIMAL_PBUFFER_HEIGHT_EXT = 8242 	# http://www.opengl.org/registry/api/wglext.h:246
WGL_PBUFFER_LARGEST_EXT = 8243 	# http://www.opengl.org/registry/api/wglext.h:247
WGL_PBUFFER_WIDTH_EXT = 8244 	# http://www.opengl.org/registry/api/wglext.h:248
WGL_PBUFFER_HEIGHT_EXT = 8245 	# http://www.opengl.org/registry/api/wglext.h:249
# EXT_depth_float (http://www.opengl.org/registry/api/wglext.h:252)
WGL_DEPTH_FLOAT_EXT = 8256 	# http://www.opengl.org/registry/api/wglext.h:253
# 3DFX_multisample (http://www.opengl.org/registry/api/wglext.h:256)
WGL_SAMPLE_BUFFERS_3DFX = 8288 	# http://www.opengl.org/registry/api/wglext.h:257
WGL_SAMPLES_3DFX = 8289 	# http://www.opengl.org/registry/api/wglext.h:258
# EXT_multisample (http://www.opengl.org/registry/api/wglext.h:261)
WGL_SAMPLE_BUFFERS_EXT = 8257 	# http://www.opengl.org/registry/api/wglext.h:262
WGL_SAMPLES_EXT = 8258 	# http://www.opengl.org/registry/api/wglext.h:263
# I3D_digital_video_control (http://www.opengl.org/registry/api/wglext.h:266)
WGL_DIGITAL_VIDEO_CURSOR_ALPHA_FRAMEBUFFER_I3D = 8272 	# http://www.opengl.org/registry/api/wglext.h:267
WGL_DIGITAL_VIDEO_CURSOR_ALPHA_VALUE_I3D = 8273 	# http://www.opengl.org/registry/api/wglext.h:268
WGL_DIGITAL_VIDEO_CURSOR_INCLUDED_I3D = 8274 	# http://www.opengl.org/registry/api/wglext.h:269
WGL_DIGITAL_VIDEO_GAMMA_CORRECTED_I3D = 8275 	# http://www.opengl.org/registry/api/wglext.h:270
# I3D_gamma (http://www.opengl.org/registry/api/wglext.h:273)
WGL_GAMMA_TABLE_SIZE_I3D = 8270 	# http://www.opengl.org/registry/api/wglext.h:274
WGL_GAMMA_EXCLUDE_DESKTOP_I3D = 8271 	# http://www.opengl.org/registry/api/wglext.h:275
# I3D_genlock (http://www.opengl.org/registry/api/wglext.h:278)
WGL_GENLOCK_SOURCE_MULTIVIEW_I3D = 8260 	# http://www.opengl.org/registry/api/wglext.h:279
WGL_GENLOCK_SOURCE_EXTENAL_SYNC_I3D = 8261 	# http://www.opengl.org/registry/api/wglext.h:280
WGL_GENLOCK_SOURCE_EXTENAL_FIELD_I3D = 8262 	# http://www.opengl.org/registry/api/wglext.h:281
WGL_GENLOCK_SOURCE_EXTENAL_TTL_I3D = 8263 	# http://www.opengl.org/registry/api/wglext.h:282
WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D = 8264 	# http://www.opengl.org/registry/api/wglext.h:283
WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D = 8265 	# http://www.opengl.org/registry/api/wglext.h:284
WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D = 8266 	# http://www.opengl.org/registry/api/wglext.h:285
WGL_GENLOCK_SOURCE_EDGE_RISING_I3D = 8267 	# http://www.opengl.org/registry/api/wglext.h:286
WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D = 8268 	# http://www.opengl.org/registry/api/wglext.h:287
# I3D_image_buffer (http://www.opengl.org/registry/api/wglext.h:290)
WGL_IMAGE_BUFFER_MIN_ACCESS_I3D = 1 	# http://www.opengl.org/registry/api/wglext.h:291
WGL_IMAGE_BUFFER_LOCK_I3D = 2 	# http://www.opengl.org/registry/api/wglext.h:292
# I3D_swap_frame_lock (http://www.opengl.org/registry/api/wglext.h:295)
# NV_render_depth_texture (http://www.opengl.org/registry/api/wglext.h:298)
WGL_BIND_TO_TEXTURE_DEPTH_NV = 8355 	# http://www.opengl.org/registry/api/wglext.h:299
WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV = 8356 	# http://www.opengl.org/registry/api/wglext.h:300
WGL_DEPTH_TEXTURE_FORMAT_NV = 8357 	# http://www.opengl.org/registry/api/wglext.h:301
WGL_TEXTURE_DEPTH_COMPONENT_NV = 8358 	# http://www.opengl.org/registry/api/wglext.h:302
WGL_DEPTH_COMPONENT_NV = 8359 	# http://www.opengl.org/registry/api/wglext.h:303
# NV_render_texture_rectangle (http://www.opengl.org/registry/api/wglext.h:306)
WGL_BIND_TO_TEXTURE_RECTANGLE_RGB_NV = 8352 	# http://www.opengl.org/registry/api/wglext.h:307
WGL_BIND_TO_TEXTURE_RECTANGLE_RGBA_NV = 8353 	# http://www.opengl.org/registry/api/wglext.h:308
WGL_TEXTURE_RECTANGLE_NV = 8354 	# http://www.opengl.org/registry/api/wglext.h:309
# ATI_pixel_format_float (http://www.opengl.org/registry/api/wglext.h:312)
WGL_TYPE_RGBA_FLOAT_ATI = 8608 	# http://www.opengl.org/registry/api/wglext.h:313
# NV_float_buffer (http://www.opengl.org/registry/api/wglext.h:316)
WGL_FLOAT_COMPONENTS_NV = 8368 	# http://www.opengl.org/registry/api/wglext.h:317
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_R_NV = 8369 	# http://www.opengl.org/registry/api/wglext.h:318
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RG_NV = 8370 	# http://www.opengl.org/registry/api/wglext.h:319
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGB_NV = 8371 	# http://www.opengl.org/registry/api/wglext.h:320
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGBA_NV = 8372 	# http://www.opengl.org/registry/api/wglext.h:321
WGL_TEXTURE_FLOAT_R_NV = 8373 	# http://www.opengl.org/registry/api/wglext.h:322
WGL_TEXTURE_FLOAT_RG_NV = 8374 	# http://www.opengl.org/registry/api/wglext.h:323
WGL_TEXTURE_FLOAT_RGB_NV = 8375 	# http://www.opengl.org/registry/api/wglext.h:324
WGL_TEXTURE_FLOAT_RGBA_NV = 8376 	# http://www.opengl.org/registry/api/wglext.h:325
# 3DL_stereo_control (http://www.opengl.org/registry/api/wglext.h:328)
WGL_STEREO_EMITTER_ENABLE_3DL = 8277 	# http://www.opengl.org/registry/api/wglext.h:329
WGL_STEREO_EMITTER_DISABLE_3DL = 8278 	# http://www.opengl.org/registry/api/wglext.h:330
WGL_STEREO_POLARITY_NORMAL_3DL = 8279 	# http://www.opengl.org/registry/api/wglext.h:331
WGL_STEREO_POLARITY_INVERT_3DL = 8280 	# http://www.opengl.org/registry/api/wglext.h:332
# EXT_pixel_format_packed_float (http://www.opengl.org/registry/api/wglext.h:335)
WGL_TYPE_RGBA_UNSIGNED_FLOAT_EXT = 8360 	# http://www.opengl.org/registry/api/wglext.h:336
# EXT_framebuffer_sRGB (http://www.opengl.org/registry/api/wglext.h:339)
WGL_FRAMEBUFFER_SRGB_CAPABLE_EXT = 8361 	# http://www.opengl.org/registry/api/wglext.h:340
# NV_present_video (http://www.opengl.org/registry/api/wglext.h:343)
WGL_NUM_VIDEO_SLOTS_NV = 8432 	# http://www.opengl.org/registry/api/wglext.h:344
# NV_video_out (http://www.opengl.org/registry/api/wglext.h:347)
WGL_BIND_TO_VIDEO_RGB_NV = 8384 	# http://www.opengl.org/registry/api/wglext.h:348
WGL_BIND_TO_VIDEO_RGBA_NV = 8385 	# http://www.opengl.org/registry/api/wglext.h:349
WGL_BIND_TO_VIDEO_RGB_AND_DEPTH_NV = 8386 	# http://www.opengl.org/registry/api/wglext.h:350
WGL_VIDEO_OUT_COLOR_NV = 8387 	# http://www.opengl.org/registry/api/wglext.h:351
WGL_VIDEO_OUT_ALPHA_NV = 8388 	# http://www.opengl.org/registry/api/wglext.h:352
WGL_VIDEO_OUT_DEPTH_NV = 8389 	# http://www.opengl.org/registry/api/wglext.h:353
WGL_VIDEO_OUT_COLOR_AND_ALPHA_NV = 8390 	# http://www.opengl.org/registry/api/wglext.h:354
WGL_VIDEO_OUT_COLOR_AND_DEPTH_NV = 8391 	# http://www.opengl.org/registry/api/wglext.h:355
WGL_VIDEO_OUT_FRAME = 8392 	# http://www.opengl.org/registry/api/wglext.h:356
WGL_VIDEO_OUT_FIELD_1 = 8393 	# http://www.opengl.org/registry/api/wglext.h:357
WGL_VIDEO_OUT_FIELD_2 = 8394 	# http://www.opengl.org/registry/api/wglext.h:358
WGL_VIDEO_OUT_STACKED_FIELDS_1_2 = 8395 	# http://www.opengl.org/registry/api/wglext.h:359
WGL_VIDEO_OUT_STACKED_FIELDS_2_1 = 8396 	# http://www.opengl.org/registry/api/wglext.h:360
# NV_swap_group (http://www.opengl.org/registry/api/wglext.h:363)
# NV_gpu_affinity (http://www.opengl.org/registry/api/wglext.h:366)
WGL_ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV = 8400 	# http://www.opengl.org/registry/api/wglext.h:367
WGL_ERROR_MISSING_AFFINITY_MASK_NV = 8401 	# http://www.opengl.org/registry/api/wglext.h:368
# ARB_pbuffer (http://www.opengl.org/registry/api/wglext.h:374)

HPBUFFERARB = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:375
# EXT_pbuffer (http://www.opengl.org/registry/api/wglext.h:377)
HPBUFFEREXT = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:378
# NV_present_video (http://www.opengl.org/registry/api/wglext.h:380)
HVIDEOOUTPUTDEVICENV = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:381
# NV_video_out (http://www.opengl.org/registry/api/wglext.h:383)
HPVIDEODEV = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:384
# NV_gpu_affinity (http://www.opengl.org/registry/api/wglext.h:386)
HPGPUNV = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:387
HGPUNV = HANDLE 	# http://www.opengl.org/registry/api/wglext.h:388
class struct__GPU_DEVICE(Structure):
    __slots__ = [
        'DeviceName',
        'DeviceString',
        'Flags',
        'cb',
        'rcVirtualScreen',
    ]

class struct_tagRECT(Structure):
    __slots__ = [
        'bottom',
        'left',
        'right',
        'top',
    ]

struct_tagRECT._fields_ = [
    ('left', LONG),
    ('top', LONG),
    ('right', LONG),
    ('bottom', LONG),
]

RECT = struct_tagRECT 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:200
struct__GPU_DEVICE._fields_ = [
    ('cb', DWORD),
    ('DeviceName', CHAR * 32),
    ('DeviceString', CHAR * 128),
    ('Flags', DWORD),
    ('rcVirtualScreen', RECT),
]

GPU_DEVICE = struct__GPU_DEVICE 	# http://www.opengl.org/registry/api/wglext.h:396
PGPU_DEVICE = POINTER(struct__GPU_DEVICE) 	# http://www.opengl.org/registry/api/wglext.h:396
# ARB_buffer_region (http://www.opengl.org/registry/api/wglext.h:399)
WGL_ARB_buffer_region = 1 	# http://www.opengl.org/registry/api/wglext.h:400

# http://www.opengl.org/registry/api/wglext.h:402
wglCreateBufferRegionARB = _link_function('wglCreateBufferRegionARB', HANDLE, [HDC, c_int, UINT], 'ARB_buffer_region')

VOID = None 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:45
# http://www.opengl.org/registry/api/wglext.h:403
wglDeleteBufferRegionARB = _link_function('wglDeleteBufferRegionARB', VOID, [HANDLE], 'ARB_buffer_region')

BOOL = c_long 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:52
# http://www.opengl.org/registry/api/wglext.h:404
wglSaveBufferRegionARB = _link_function('wglSaveBufferRegionARB', BOOL, [HANDLE, c_int, c_int, c_int, c_int], 'ARB_buffer_region')

# http://www.opengl.org/registry/api/wglext.h:405
wglRestoreBufferRegionARB = _link_function('wglRestoreBufferRegionARB', BOOL, [HANDLE, c_int, c_int, c_int, c_int, c_int, c_int], 'ARB_buffer_region')

PFNWGLCREATEBUFFERREGIONARBPROC = CFUNCTYPE(HANDLE, HDC, c_int, UINT) 	# http://www.opengl.org/registry/api/wglext.h:407
PFNWGLDELETEBUFFERREGIONARBPROC = CFUNCTYPE(VOID, HANDLE) 	# http://www.opengl.org/registry/api/wglext.h:408
PFNWGLSAVEBUFFERREGIONARBPROC = CFUNCTYPE(BOOL, HANDLE, c_int, c_int, c_int, c_int) 	# http://www.opengl.org/registry/api/wglext.h:409
PFNWGLRESTOREBUFFERREGIONARBPROC = CFUNCTYPE(BOOL, HANDLE, c_int, c_int, c_int, c_int, c_int, c_int) 	# http://www.opengl.org/registry/api/wglext.h:410
# ARB_multisample (http://www.opengl.org/registry/api/wglext.h:413)
WGL_ARB_multisample = 1 	# http://www.opengl.org/registry/api/wglext.h:414
# ARB_extensions_string (http://www.opengl.org/registry/api/wglext.h:417)
WGL_ARB_extensions_string = 1 	# http://www.opengl.org/registry/api/wglext.h:418
# http://www.opengl.org/registry/api/wglext.h:420
wglGetExtensionsStringARB = _link_function('wglGetExtensionsStringARB', c_char_p, [HDC], 'ARB_extensions_string')

PFNWGLGETEXTENSIONSSTRINGARBPROC = CFUNCTYPE(c_char_p, HDC) 	# http://www.opengl.org/registry/api/wglext.h:422
# ARB_pixel_format (http://www.opengl.org/registry/api/wglext.h:425)
WGL_ARB_pixel_format = 1 	# http://www.opengl.org/registry/api/wglext.h:426
# http://www.opengl.org/registry/api/wglext.h:428
wglGetPixelFormatAttribivARB = _link_function('wglGetPixelFormatAttribivARB', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)], 'ARB_pixel_format')

# http://www.opengl.org/registry/api/wglext.h:429
wglGetPixelFormatAttribfvARB = _link_function('wglGetPixelFormatAttribfvARB', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)], 'ARB_pixel_format')

# http://www.opengl.org/registry/api/wglext.h:430
wglChoosePixelFormatARB = _link_function('wglChoosePixelFormatARB', BOOL, [HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)], 'ARB_pixel_format')

PFNWGLGETPIXELFORMATATTRIBIVARBPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:432
PFNWGLGETPIXELFORMATATTRIBFVARBPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)) 	# http://www.opengl.org/registry/api/wglext.h:433
PFNWGLCHOOSEPIXELFORMATARBPROC = CFUNCTYPE(BOOL, HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:434
# ARB_make_current_read (http://www.opengl.org/registry/api/wglext.h:437)
WGL_ARB_make_current_read = 1 	# http://www.opengl.org/registry/api/wglext.h:438
HGLRC = HANDLE 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:60
# http://www.opengl.org/registry/api/wglext.h:440
wglMakeContextCurrentARB = _link_function('wglMakeContextCurrentARB', BOOL, [HDC, HDC, HGLRC], 'ARB_make_current_read')

# http://www.opengl.org/registry/api/wglext.h:441
wglGetCurrentReadDCARB = _link_function('wglGetCurrentReadDCARB', HDC, [], 'ARB_make_current_read')

PFNWGLMAKECONTEXTCURRENTARBPROC = CFUNCTYPE(BOOL, HDC, HDC, HGLRC) 	# http://www.opengl.org/registry/api/wglext.h:443
PFNWGLGETCURRENTREADDCARBPROC = CFUNCTYPE(HDC) 	# http://www.opengl.org/registry/api/wglext.h:444
# ARB_pbuffer (http://www.opengl.org/registry/api/wglext.h:447)
WGL_ARB_pbuffer = 1 	# http://www.opengl.org/registry/api/wglext.h:448
# http://www.opengl.org/registry/api/wglext.h:450
wglCreatePbufferARB = _link_function('wglCreatePbufferARB', HPBUFFERARB, [HDC, c_int, c_int, c_int, POINTER(c_int)], 'ARB_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:451
wglGetPbufferDCARB = _link_function('wglGetPbufferDCARB', HDC, [HPBUFFERARB], 'ARB_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:452
wglReleasePbufferDCARB = _link_function('wglReleasePbufferDCARB', c_int, [HPBUFFERARB, HDC], 'ARB_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:453
wglDestroyPbufferARB = _link_function('wglDestroyPbufferARB', BOOL, [HPBUFFERARB], 'ARB_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:454
wglQueryPbufferARB = _link_function('wglQueryPbufferARB', BOOL, [HPBUFFERARB, c_int, POINTER(c_int)], 'ARB_pbuffer')

PFNWGLCREATEPBUFFERARBPROC = CFUNCTYPE(HPBUFFERARB, HDC, c_int, c_int, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:456
PFNWGLGETPBUFFERDCARBPROC = CFUNCTYPE(HDC, HPBUFFERARB) 	# http://www.opengl.org/registry/api/wglext.h:457
PFNWGLRELEASEPBUFFERDCARBPROC = CFUNCTYPE(c_int, HPBUFFERARB, HDC) 	# http://www.opengl.org/registry/api/wglext.h:458
PFNWGLDESTROYPBUFFERARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB) 	# http://www.opengl.org/registry/api/wglext.h:459
PFNWGLQUERYPBUFFERARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:460
# ARB_render_texture (http://www.opengl.org/registry/api/wglext.h:463)
WGL_ARB_render_texture = 1 	# http://www.opengl.org/registry/api/wglext.h:464
# http://www.opengl.org/registry/api/wglext.h:466
wglBindTexImageARB = _link_function('wglBindTexImageARB', BOOL, [HPBUFFERARB, c_int], 'ARB_render_texture')

# http://www.opengl.org/registry/api/wglext.h:467
wglReleaseTexImageARB = _link_function('wglReleaseTexImageARB', BOOL, [HPBUFFERARB, c_int], 'ARB_render_texture')

# http://www.opengl.org/registry/api/wglext.h:468
wglSetPbufferAttribARB = _link_function('wglSetPbufferAttribARB', BOOL, [HPBUFFERARB, POINTER(c_int)], 'ARB_render_texture')

PFNWGLBINDTEXIMAGEARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int) 	# http://www.opengl.org/registry/api/wglext.h:470
PFNWGLRELEASETEXIMAGEARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int) 	# http://www.opengl.org/registry/api/wglext.h:471
PFNWGLSETPBUFFERATTRIBARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:472
# ARB_pixel_format_float (http://www.opengl.org/registry/api/wglext.h:475)
WGL_ARB_pixel_format_float = 1 	# http://www.opengl.org/registry/api/wglext.h:476
# ARB_create_context (http://www.opengl.org/registry/api/wglext.h:479)
WGL_ARB_create_context = 1 	# http://www.opengl.org/registry/api/wglext.h:480
# http://www.opengl.org/registry/api/wglext.h:482
wglCreateContextAttribsARB = _link_function('wglCreateContextAttribsARB', HGLRC, [HDC, HGLRC, POINTER(c_int)], 'ARB_create_context')

PFNWGLCREATECONTEXTATTRIBSARBPROC = CFUNCTYPE(HGLRC, HDC, HGLRC, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:484
# EXT_display_color_table (http://www.opengl.org/registry/api/wglext.h:487)
WGL_EXT_display_color_table = 1 	# http://www.opengl.org/registry/api/wglext.h:488
GLboolean = c_ubyte 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:18
GLushort = c_ushort 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:25
# http://www.opengl.org/registry/api/wglext.h:490
wglCreateDisplayColorTableEXT = _link_function('wglCreateDisplayColorTableEXT', GLboolean, [GLushort], 'EXT_display_color_table')

GLuint = c_uint 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:26
# http://www.opengl.org/registry/api/wglext.h:491
wglLoadDisplayColorTableEXT = _link_function('wglLoadDisplayColorTableEXT', GLboolean, [POINTER(GLushort), GLuint], 'EXT_display_color_table')

# http://www.opengl.org/registry/api/wglext.h:492
wglBindDisplayColorTableEXT = _link_function('wglBindDisplayColorTableEXT', GLboolean, [GLushort], 'EXT_display_color_table')

# http://www.opengl.org/registry/api/wglext.h:493
wglDestroyDisplayColorTableEXT = _link_function('wglDestroyDisplayColorTableEXT', VOID, [GLushort], 'EXT_display_color_table')

PFNWGLCREATEDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, GLushort) 	# http://www.opengl.org/registry/api/wglext.h:495
PFNWGLLOADDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, POINTER(GLushort), GLuint) 	# http://www.opengl.org/registry/api/wglext.h:496
PFNWGLBINDDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, GLushort) 	# http://www.opengl.org/registry/api/wglext.h:497
PFNWGLDESTROYDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(VOID, GLushort) 	# http://www.opengl.org/registry/api/wglext.h:498
# EXT_extensions_string (http://www.opengl.org/registry/api/wglext.h:501)
WGL_EXT_extensions_string = 1 	# http://www.opengl.org/registry/api/wglext.h:502
# http://www.opengl.org/registry/api/wglext.h:504
wglGetExtensionsStringEXT = _link_function('wglGetExtensionsStringEXT', c_char_p, [], 'EXT_extensions_string')

PFNWGLGETEXTENSIONSSTRINGEXTPROC = CFUNCTYPE(c_char_p) 	# http://www.opengl.org/registry/api/wglext.h:506
# EXT_make_current_read (http://www.opengl.org/registry/api/wglext.h:509)
WGL_EXT_make_current_read = 1 	# http://www.opengl.org/registry/api/wglext.h:510
# http://www.opengl.org/registry/api/wglext.h:512
wglMakeContextCurrentEXT = _link_function('wglMakeContextCurrentEXT', BOOL, [HDC, HDC, HGLRC], 'EXT_make_current_read')

# http://www.opengl.org/registry/api/wglext.h:513
wglGetCurrentReadDCEXT = _link_function('wglGetCurrentReadDCEXT', HDC, [], 'EXT_make_current_read')

PFNWGLMAKECONTEXTCURRENTEXTPROC = CFUNCTYPE(BOOL, HDC, HDC, HGLRC) 	# http://www.opengl.org/registry/api/wglext.h:515
PFNWGLGETCURRENTREADDCEXTPROC = CFUNCTYPE(HDC) 	# http://www.opengl.org/registry/api/wglext.h:516
# EXT_pbuffer (http://www.opengl.org/registry/api/wglext.h:519)
WGL_EXT_pbuffer = 1 	# http://www.opengl.org/registry/api/wglext.h:520
# http://www.opengl.org/registry/api/wglext.h:522
wglCreatePbufferEXT = _link_function('wglCreatePbufferEXT', HPBUFFEREXT, [HDC, c_int, c_int, c_int, POINTER(c_int)], 'EXT_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:523
wglGetPbufferDCEXT = _link_function('wglGetPbufferDCEXT', HDC, [HPBUFFEREXT], 'EXT_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:524
wglReleasePbufferDCEXT = _link_function('wglReleasePbufferDCEXT', c_int, [HPBUFFEREXT, HDC], 'EXT_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:525
wglDestroyPbufferEXT = _link_function('wglDestroyPbufferEXT', BOOL, [HPBUFFEREXT], 'EXT_pbuffer')

# http://www.opengl.org/registry/api/wglext.h:526
wglQueryPbufferEXT = _link_function('wglQueryPbufferEXT', BOOL, [HPBUFFEREXT, c_int, POINTER(c_int)], 'EXT_pbuffer')

PFNWGLCREATEPBUFFEREXTPROC = CFUNCTYPE(HPBUFFEREXT, HDC, c_int, c_int, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:528
PFNWGLGETPBUFFERDCEXTPROC = CFUNCTYPE(HDC, HPBUFFEREXT) 	# http://www.opengl.org/registry/api/wglext.h:529
PFNWGLRELEASEPBUFFERDCEXTPROC = CFUNCTYPE(c_int, HPBUFFEREXT, HDC) 	# http://www.opengl.org/registry/api/wglext.h:530
PFNWGLDESTROYPBUFFEREXTPROC = CFUNCTYPE(BOOL, HPBUFFEREXT) 	# http://www.opengl.org/registry/api/wglext.h:531
PFNWGLQUERYPBUFFEREXTPROC = CFUNCTYPE(BOOL, HPBUFFEREXT, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:532
# EXT_pixel_format (http://www.opengl.org/registry/api/wglext.h:535)
WGL_EXT_pixel_format = 1 	# http://www.opengl.org/registry/api/wglext.h:536
# http://www.opengl.org/registry/api/wglext.h:538
wglGetPixelFormatAttribivEXT = _link_function('wglGetPixelFormatAttribivEXT', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)], 'EXT_pixel_format')

# http://www.opengl.org/registry/api/wglext.h:539
wglGetPixelFormatAttribfvEXT = _link_function('wglGetPixelFormatAttribfvEXT', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)], 'EXT_pixel_format')

# http://www.opengl.org/registry/api/wglext.h:540
wglChoosePixelFormatEXT = _link_function('wglChoosePixelFormatEXT', BOOL, [HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)], 'EXT_pixel_format')

PFNWGLGETPIXELFORMATATTRIBIVEXTPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:542
PFNWGLGETPIXELFORMATATTRIBFVEXTPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)) 	# http://www.opengl.org/registry/api/wglext.h:543
PFNWGLCHOOSEPIXELFORMATEXTPROC = CFUNCTYPE(BOOL, HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:544
# EXT_swap_control (http://www.opengl.org/registry/api/wglext.h:547)
WGL_EXT_swap_control = 1 	# http://www.opengl.org/registry/api/wglext.h:548
# http://www.opengl.org/registry/api/wglext.h:550
wglSwapIntervalEXT = _link_function('wglSwapIntervalEXT', BOOL, [c_int], 'EXT_swap_control')

# http://www.opengl.org/registry/api/wglext.h:551
wglGetSwapIntervalEXT = _link_function('wglGetSwapIntervalEXT', c_int, [], 'EXT_swap_control')

PFNWGLSWAPINTERVALEXTPROC = CFUNCTYPE(BOOL, c_int) 	# http://www.opengl.org/registry/api/wglext.h:553
PFNWGLGETSWAPINTERVALEXTPROC = CFUNCTYPE(c_int) 	# http://www.opengl.org/registry/api/wglext.h:554
# EXT_depth_float (http://www.opengl.org/registry/api/wglext.h:557)
WGL_EXT_depth_float = 1 	# http://www.opengl.org/registry/api/wglext.h:558
# NV_vertex_array_range (http://www.opengl.org/registry/api/wglext.h:561)
WGL_NV_vertex_array_range = 1 	# http://www.opengl.org/registry/api/wglext.h:562
GLsizei = c_int 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:23
GLfloat = c_float 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:27
# http://www.opengl.org/registry/api/wglext.h:564
wglAllocateMemoryNV = _link_function('wglAllocateMemoryNV', POINTER(c_void), [GLsizei, GLfloat, GLfloat, GLfloat], 'NV_vertex_array_range')

# http://www.opengl.org/registry/api/wglext.h:565
wglFreeMemoryNV = _link_function('wglFreeMemoryNV', None, [POINTER(None)], 'NV_vertex_array_range')

PFNWGLALLOCATEMEMORYNVPROC = CFUNCTYPE(POINTER(c_void), GLsizei, GLfloat, GLfloat, GLfloat) 	# http://www.opengl.org/registry/api/wglext.h:567
PFNWGLFREEMEMORYNVPROC = CFUNCTYPE(None, POINTER(None)) 	# http://www.opengl.org/registry/api/wglext.h:568
# 3DFX_multisample (http://www.opengl.org/registry/api/wglext.h:571)
WGL_3DFX_multisample = 1 	# http://www.opengl.org/registry/api/wglext.h:572
# EXT_multisample (http://www.opengl.org/registry/api/wglext.h:575)
WGL_EXT_multisample = 1 	# http://www.opengl.org/registry/api/wglext.h:576
# OML_sync_control (http://www.opengl.org/registry/api/wglext.h:579)
WGL_OML_sync_control = 1 	# http://www.opengl.org/registry/api/wglext.h:580

# http://www.opengl.org/registry/api/wglext.h:582
wglGetSyncValuesOML = _link_function('wglGetSyncValuesOML', BOOL, [HDC, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

# http://www.opengl.org/registry/api/wglext.h:583
wglGetMscRateOML = _link_function('wglGetMscRateOML', BOOL, [HDC, POINTER(INT32), POINTER(INT32)], 'OML_sync_control')

# http://www.opengl.org/registry/api/wglext.h:584
wglSwapBuffersMscOML = _link_function('wglSwapBuffersMscOML', INT64, [HDC, INT64, INT64, INT64], 'OML_sync_control')

# http://www.opengl.org/registry/api/wglext.h:585
wglSwapLayerBuffersMscOML = _link_function('wglSwapLayerBuffersMscOML', INT64, [HDC, c_int, INT64, INT64, INT64], 'OML_sync_control')

# http://www.opengl.org/registry/api/wglext.h:586
wglWaitForMscOML = _link_function('wglWaitForMscOML', BOOL, [HDC, INT64, INT64, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

# http://www.opengl.org/registry/api/wglext.h:587
wglWaitForSbcOML = _link_function('wglWaitForSbcOML', BOOL, [HDC, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

PFNWGLGETSYNCVALUESOMLPROC = CFUNCTYPE(BOOL, HDC, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://www.opengl.org/registry/api/wglext.h:589
PFNWGLGETMSCRATEOMLPROC = CFUNCTYPE(BOOL, HDC, POINTER(INT32), POINTER(INT32)) 	# http://www.opengl.org/registry/api/wglext.h:590
PFNWGLSWAPBUFFERSMSCOMLPROC = CFUNCTYPE(INT64, HDC, INT64, INT64, INT64) 	# http://www.opengl.org/registry/api/wglext.h:591
PFNWGLSWAPLAYERBUFFERSMSCOMLPROC = CFUNCTYPE(INT64, HDC, c_int, INT64, INT64, INT64) 	# http://www.opengl.org/registry/api/wglext.h:592
PFNWGLWAITFORMSCOMLPROC = CFUNCTYPE(BOOL, HDC, INT64, INT64, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://www.opengl.org/registry/api/wglext.h:593
PFNWGLWAITFORSBCOMLPROC = CFUNCTYPE(BOOL, HDC, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://www.opengl.org/registry/api/wglext.h:594
# I3D_digital_video_control (http://www.opengl.org/registry/api/wglext.h:597)
WGL_I3D_digital_video_control = 1 	# http://www.opengl.org/registry/api/wglext.h:598
# http://www.opengl.org/registry/api/wglext.h:600
wglGetDigitalVideoParametersI3D = _link_function('wglGetDigitalVideoParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_digital_video_control')

# http://www.opengl.org/registry/api/wglext.h:601
wglSetDigitalVideoParametersI3D = _link_function('wglSetDigitalVideoParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_digital_video_control')

PFNWGLGETDIGITALVIDEOPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:603
PFNWGLSETDIGITALVIDEOPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:604
# I3D_gamma (http://www.opengl.org/registry/api/wglext.h:607)
WGL_I3D_gamma = 1 	# http://www.opengl.org/registry/api/wglext.h:608
# http://www.opengl.org/registry/api/wglext.h:610
wglGetGammaTableParametersI3D = _link_function('wglGetGammaTableParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_gamma')

# http://www.opengl.org/registry/api/wglext.h:611
wglSetGammaTableParametersI3D = _link_function('wglSetGammaTableParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_gamma')

# http://www.opengl.org/registry/api/wglext.h:612
wglGetGammaTableI3D = _link_function('wglGetGammaTableI3D', BOOL, [HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)], 'I3D_gamma')

# http://www.opengl.org/registry/api/wglext.h:613
wglSetGammaTableI3D = _link_function('wglSetGammaTableI3D', BOOL, [HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)], 'I3D_gamma')

PFNWGLGETGAMMATABLEPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:615
PFNWGLSETGAMMATABLEPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:616
PFNWGLGETGAMMATABLEI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)) 	# http://www.opengl.org/registry/api/wglext.h:617
PFNWGLSETGAMMATABLEI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)) 	# http://www.opengl.org/registry/api/wglext.h:618
# I3D_genlock (http://www.opengl.org/registry/api/wglext.h:621)
WGL_I3D_genlock = 1 	# http://www.opengl.org/registry/api/wglext.h:622
# http://www.opengl.org/registry/api/wglext.h:624
wglEnableGenlockI3D = _link_function('wglEnableGenlockI3D', BOOL, [HDC], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:625
wglDisableGenlockI3D = _link_function('wglDisableGenlockI3D', BOOL, [HDC], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:626
wglIsEnabledGenlockI3D = _link_function('wglIsEnabledGenlockI3D', BOOL, [HDC, POINTER(BOOL)], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:627
wglGenlockSourceI3D = _link_function('wglGenlockSourceI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:628
wglGetGenlockSourceI3D = _link_function('wglGetGenlockSourceI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:629
wglGenlockSourceEdgeI3D = _link_function('wglGenlockSourceEdgeI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:630
wglGetGenlockSourceEdgeI3D = _link_function('wglGetGenlockSourceEdgeI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:631
wglGenlockSampleRateI3D = _link_function('wglGenlockSampleRateI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:632
wglGetGenlockSampleRateI3D = _link_function('wglGetGenlockSampleRateI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:633
wglGenlockSourceDelayI3D = _link_function('wglGenlockSourceDelayI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:634
wglGetGenlockSourceDelayI3D = _link_function('wglGetGenlockSourceDelayI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://www.opengl.org/registry/api/wglext.h:635
wglQueryGenlockMaxSourceDelayI3D = _link_function('wglQueryGenlockMaxSourceDelayI3D', BOOL, [HDC, POINTER(UINT), POINTER(UINT)], 'I3D_genlock')

PFNWGLENABLEGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC) 	# http://www.opengl.org/registry/api/wglext.h:637
PFNWGLDISABLEGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC) 	# http://www.opengl.org/registry/api/wglext.h:638
PFNWGLISENABLEDGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(BOOL)) 	# http://www.opengl.org/registry/api/wglext.h:639
PFNWGLGENLOCKSOURCEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://www.opengl.org/registry/api/wglext.h:640
PFNWGLGETGENLOCKSOURCEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:641
PFNWGLGENLOCKSOURCEEDGEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://www.opengl.org/registry/api/wglext.h:642
PFNWGLGETGENLOCKSOURCEEDGEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:643
PFNWGLGENLOCKSAMPLERATEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://www.opengl.org/registry/api/wglext.h:644
PFNWGLGETGENLOCKSAMPLERATEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:645
PFNWGLGENLOCKSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://www.opengl.org/registry/api/wglext.h:646
PFNWGLGETGENLOCKSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:647
PFNWGLQUERYGENLOCKMAXSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT), POINTER(UINT)) 	# http://www.opengl.org/registry/api/wglext.h:648
# I3D_image_buffer (http://www.opengl.org/registry/api/wglext.h:651)
WGL_I3D_image_buffer = 1 	# http://www.opengl.org/registry/api/wglext.h:652

# http://www.opengl.org/registry/api/wglext.h:654
wglCreateImageBufferI3D = _link_function('wglCreateImageBufferI3D', LPVOID, [HDC, DWORD, UINT], 'I3D_image_buffer')

# http://www.opengl.org/registry/api/wglext.h:655
wglDestroyImageBufferI3D = _link_function('wglDestroyImageBufferI3D', BOOL, [HDC, LPVOID], 'I3D_image_buffer')

# http://www.opengl.org/registry/api/wglext.h:656
wglAssociateImageBufferEventsI3D = _link_function('wglAssociateImageBufferEventsI3D', BOOL, [HDC, POINTER(HANDLE), POINTER(LPVOID), POINTER(DWORD), UINT], 'I3D_image_buffer')

# http://www.opengl.org/registry/api/wglext.h:657
wglReleaseImageBufferEventsI3D = _link_function('wglReleaseImageBufferEventsI3D', BOOL, [HDC, POINTER(LPVOID), UINT], 'I3D_image_buffer')

PFNWGLCREATEIMAGEBUFFERI3DPROC = CFUNCTYPE(LPVOID, HDC, DWORD, UINT) 	# http://www.opengl.org/registry/api/wglext.h:659
PFNWGLDESTROYIMAGEBUFFERI3DPROC = CFUNCTYPE(BOOL, HDC, LPVOID) 	# http://www.opengl.org/registry/api/wglext.h:660
PFNWGLASSOCIATEIMAGEBUFFEREVENTSI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(HANDLE), POINTER(LPVOID), POINTER(DWORD), UINT) 	# http://www.opengl.org/registry/api/wglext.h:661
PFNWGLRELEASEIMAGEBUFFEREVENTSI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(LPVOID), UINT) 	# http://www.opengl.org/registry/api/wglext.h:662
# I3D_swap_frame_lock (http://www.opengl.org/registry/api/wglext.h:665)
WGL_I3D_swap_frame_lock = 1 	# http://www.opengl.org/registry/api/wglext.h:666
# http://www.opengl.org/registry/api/wglext.h:668
wglEnableFrameLockI3D = _link_function('wglEnableFrameLockI3D', BOOL, [], 'I3D_swap_frame_lock')

# http://www.opengl.org/registry/api/wglext.h:669
wglDisableFrameLockI3D = _link_function('wglDisableFrameLockI3D', BOOL, [], 'I3D_swap_frame_lock')

# http://www.opengl.org/registry/api/wglext.h:670
wglIsEnabledFrameLockI3D = _link_function('wglIsEnabledFrameLockI3D', BOOL, [POINTER(BOOL)], 'I3D_swap_frame_lock')

# http://www.opengl.org/registry/api/wglext.h:671
wglQueryFrameLockMasterI3D = _link_function('wglQueryFrameLockMasterI3D', BOOL, [POINTER(BOOL)], 'I3D_swap_frame_lock')

PFNWGLENABLEFRAMELOCKI3DPROC = CFUNCTYPE(BOOL) 	# http://www.opengl.org/registry/api/wglext.h:673
PFNWGLDISABLEFRAMELOCKI3DPROC = CFUNCTYPE(BOOL) 	# http://www.opengl.org/registry/api/wglext.h:674
PFNWGLISENABLEDFRAMELOCKI3DPROC = CFUNCTYPE(BOOL, POINTER(BOOL)) 	# http://www.opengl.org/registry/api/wglext.h:675
PFNWGLQUERYFRAMELOCKMASTERI3DPROC = CFUNCTYPE(BOOL, POINTER(BOOL)) 	# http://www.opengl.org/registry/api/wglext.h:676
# I3D_swap_frame_usage (http://www.opengl.org/registry/api/wglext.h:679)
WGL_I3D_swap_frame_usage = 1 	# http://www.opengl.org/registry/api/wglext.h:680
# http://www.opengl.org/registry/api/wglext.h:682
wglGetFrameUsageI3D = _link_function('wglGetFrameUsageI3D', BOOL, [POINTER(c_float)], 'I3D_swap_frame_usage')

# http://www.opengl.org/registry/api/wglext.h:683
wglBeginFrameTrackingI3D = _link_function('wglBeginFrameTrackingI3D', BOOL, [], 'I3D_swap_frame_usage')

# http://www.opengl.org/registry/api/wglext.h:684
wglEndFrameTrackingI3D = _link_function('wglEndFrameTrackingI3D', BOOL, [], 'I3D_swap_frame_usage')

# http://www.opengl.org/registry/api/wglext.h:685
wglQueryFrameTrackingI3D = _link_function('wglQueryFrameTrackingI3D', BOOL, [POINTER(DWORD), POINTER(DWORD), POINTER(c_float)], 'I3D_swap_frame_usage')

PFNWGLGETFRAMEUSAGEI3DPROC = CFUNCTYPE(BOOL, POINTER(c_float)) 	# http://www.opengl.org/registry/api/wglext.h:687
PFNWGLBEGINFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL) 	# http://www.opengl.org/registry/api/wglext.h:688
PFNWGLENDFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL) 	# http://www.opengl.org/registry/api/wglext.h:689
PFNWGLQUERYFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL, POINTER(DWORD), POINTER(DWORD), POINTER(c_float)) 	# http://www.opengl.org/registry/api/wglext.h:690
# ATI_pixel_format_float (http://www.opengl.org/registry/api/wglext.h:693)
WGL_ATI_pixel_format_float = 1 	# http://www.opengl.org/registry/api/wglext.h:694
# NV_float_buffer (http://www.opengl.org/registry/api/wglext.h:697)
WGL_NV_float_buffer = 1 	# http://www.opengl.org/registry/api/wglext.h:698
# EXT_pixel_format_packed_float (http://www.opengl.org/registry/api/wglext.h:701)
WGL_EXT_pixel_format_packed_float = 1 	# http://www.opengl.org/registry/api/wglext.h:702
# EXT_framebuffer_sRGB (http://www.opengl.org/registry/api/wglext.h:705)
WGL_EXT_framebuffer_sRGB = 1 	# http://www.opengl.org/registry/api/wglext.h:706
# NV_present_video (http://www.opengl.org/registry/api/wglext.h:709)
WGL_NV_present_video = 1 	# http://www.opengl.org/registry/api/wglext.h:710
# http://www.opengl.org/registry/api/wglext.h:712
wglEnumerateVideoDevicesNV = _link_function('wglEnumerateVideoDevicesNV', c_int, [HDC, POINTER(HVIDEOOUTPUTDEVICENV)], 'NV_present_video')

# http://www.opengl.org/registry/api/wglext.h:713
wglBindVideoDeviceNV = _link_function('wglBindVideoDeviceNV', BOOL, [HDC, c_uint, HVIDEOOUTPUTDEVICENV, POINTER(c_int)], 'NV_present_video')

# http://www.opengl.org/registry/api/wglext.h:714
wglQueryCurrentContextNV = _link_function('wglQueryCurrentContextNV', BOOL, [c_int, POINTER(c_int)], 'NV_present_video')

PFNWGLENUMERATEVIDEODEVICESNVPROC = CFUNCTYPE(c_int, HDC, POINTER(HVIDEOOUTPUTDEVICENV)) 	# http://www.opengl.org/registry/api/wglext.h:716
PFNWGLBINDVIDEODEVICENVPROC = CFUNCTYPE(BOOL, HDC, c_uint, HVIDEOOUTPUTDEVICENV, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:717
PFNWGLQUERYCURRENTCONTEXTNVPROC = CFUNCTYPE(BOOL, c_int, POINTER(c_int)) 	# http://www.opengl.org/registry/api/wglext.h:718
# NV_video_out (http://www.opengl.org/registry/api/wglext.h:721)
WGL_NV_video_out = 1 	# http://www.opengl.org/registry/api/wglext.h:722
# http://www.opengl.org/registry/api/wglext.h:724
wglGetVideoDeviceNV = _link_function('wglGetVideoDeviceNV', BOOL, [HDC, c_int, POINTER(HPVIDEODEV)], 'NV_video_out')

# http://www.opengl.org/registry/api/wglext.h:725
wglReleaseVideoDeviceNV = _link_function('wglReleaseVideoDeviceNV', BOOL, [HPVIDEODEV], 'NV_video_out')

# http://www.opengl.org/registry/api/wglext.h:726
wglBindVideoImageNV = _link_function('wglBindVideoImageNV', BOOL, [HPVIDEODEV, HPBUFFERARB, c_int], 'NV_video_out')

# http://www.opengl.org/registry/api/wglext.h:727
wglReleaseVideoImageNV = _link_function('wglReleaseVideoImageNV', BOOL, [HPBUFFERARB, c_int], 'NV_video_out')

# http://www.opengl.org/registry/api/wglext.h:728
wglSendPbufferToVideoNV = _link_function('wglSendPbufferToVideoNV', BOOL, [HPBUFFERARB, c_int, POINTER(c_ulong), BOOL], 'NV_video_out')

# http://www.opengl.org/registry/api/wglext.h:729
wglGetVideoInfoNV = _link_function('wglGetVideoInfoNV', BOOL, [HPVIDEODEV, POINTER(c_ulong), POINTER(c_ulong)], 'NV_video_out')

PFNWGLGETVIDEODEVICENVPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(HPVIDEODEV)) 	# http://www.opengl.org/registry/api/wglext.h:731
PFNWGLRELEASEVIDEODEVICENVPROC = CFUNCTYPE(BOOL, HPVIDEODEV) 	# http://www.opengl.org/registry/api/wglext.h:732
PFNWGLBINDVIDEOIMAGENVPROC = CFUNCTYPE(BOOL, HPVIDEODEV, HPBUFFERARB, c_int) 	# http://www.opengl.org/registry/api/wglext.h:733
PFNWGLRELEASEVIDEOIMAGENVPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int) 	# http://www.opengl.org/registry/api/wglext.h:734
PFNWGLSENDPBUFFERTOVIDEONVPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int, POINTER(c_ulong), BOOL) 	# http://www.opengl.org/registry/api/wglext.h:735
PFNWGLGETVIDEOINFONVPROC = CFUNCTYPE(BOOL, HPVIDEODEV, POINTER(c_ulong), POINTER(c_ulong)) 	# http://www.opengl.org/registry/api/wglext.h:736
# NV_swap_group (http://www.opengl.org/registry/api/wglext.h:739)
WGL_NV_swap_group = 1 	# http://www.opengl.org/registry/api/wglext.h:740
# http://www.opengl.org/registry/api/wglext.h:742
wglJoinSwapGroupNV = _link_function('wglJoinSwapGroupNV', BOOL, [HDC, GLuint], 'NV_swap_group')

# http://www.opengl.org/registry/api/wglext.h:743
wglBindSwapBarrierNV = _link_function('wglBindSwapBarrierNV', BOOL, [GLuint, GLuint], 'NV_swap_group')

# http://www.opengl.org/registry/api/wglext.h:744
wglQuerySwapGroupNV = _link_function('wglQuerySwapGroupNV', BOOL, [HDC, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# http://www.opengl.org/registry/api/wglext.h:745
wglQueryMaxSwapGroupsNV = _link_function('wglQueryMaxSwapGroupsNV', BOOL, [HDC, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# http://www.opengl.org/registry/api/wglext.h:746
wglQueryFrameCountNV = _link_function('wglQueryFrameCountNV', BOOL, [HDC, POINTER(GLuint)], 'NV_swap_group')

# http://www.opengl.org/registry/api/wglext.h:747
wglResetFrameCountNV = _link_function('wglResetFrameCountNV', BOOL, [HDC], 'NV_swap_group')

PFNWGLJOINSWAPGROUPNVPROC = CFUNCTYPE(BOOL, HDC, GLuint) 	# http://www.opengl.org/registry/api/wglext.h:749
PFNWGLBINDSWAPBARRIERNVPROC = CFUNCTYPE(BOOL, GLuint, GLuint) 	# http://www.opengl.org/registry/api/wglext.h:750
PFNWGLQUERYSWAPGROUPNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint), POINTER(GLuint)) 	# http://www.opengl.org/registry/api/wglext.h:751
PFNWGLQUERYMAXSWAPGROUPSNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint), POINTER(GLuint)) 	# http://www.opengl.org/registry/api/wglext.h:752
PFNWGLQUERYFRAMECOUNTNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint)) 	# http://www.opengl.org/registry/api/wglext.h:753
PFNWGLRESETFRAMECOUNTNVPROC = CFUNCTYPE(BOOL, HDC) 	# http://www.opengl.org/registry/api/wglext.h:754
# NV_gpu_affinity (http://www.opengl.org/registry/api/wglext.h:757)
WGL_NV_gpu_affinity = 1 	# http://www.opengl.org/registry/api/wglext.h:758
# http://www.opengl.org/registry/api/wglext.h:760
wglEnumGpusNV = _link_function('wglEnumGpusNV', BOOL, [UINT, POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://www.opengl.org/registry/api/wglext.h:761
wglEnumGpuDevicesNV = _link_function('wglEnumGpuDevicesNV', BOOL, [HGPUNV, UINT, PGPU_DEVICE], 'NV_gpu_affinity')

# http://www.opengl.org/registry/api/wglext.h:762
wglCreateAffinityDCNV = _link_function('wglCreateAffinityDCNV', HDC, [POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://www.opengl.org/registry/api/wglext.h:763
wglEnumGpusFromAffinityDCNV = _link_function('wglEnumGpusFromAffinityDCNV', BOOL, [HDC, UINT, POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://www.opengl.org/registry/api/wglext.h:764
wglDeleteDCNV = _link_function('wglDeleteDCNV', BOOL, [HDC], 'NV_gpu_affinity')

PFNWGLENUMGPUSNVPROC = CFUNCTYPE(BOOL, UINT, POINTER(HGPUNV)) 	# http://www.opengl.org/registry/api/wglext.h:766
PFNWGLENUMGPUDEVICESNVPROC = CFUNCTYPE(BOOL, HGPUNV, UINT, PGPU_DEVICE) 	# http://www.opengl.org/registry/api/wglext.h:767
PFNWGLCREATEAFFINITYDCNVPROC = CFUNCTYPE(HDC, POINTER(HGPUNV)) 	# http://www.opengl.org/registry/api/wglext.h:768
PFNWGLENUMGPUSFROMAFFINITYDCNVPROC = CFUNCTYPE(BOOL, HDC, UINT, POINTER(HGPUNV)) 	# http://www.opengl.org/registry/api/wglext.h:769
PFNWGLDELETEDCNVPROC = CFUNCTYPE(BOOL, HDC) 	# http://www.opengl.org/registry/api/wglext.h:770

__all__ = [
    'ERROR_INCOMPATIBLE_DEVICE_CONTEXTS_ARB',
    'ERROR_INVALID_PIXEL_TYPE_ARB',
    'ERROR_INVALID_PIXEL_TYPE_EXT',
    'ERROR_INVALID_VERSION_ARB',
    'GPU_DEVICE',
    'HGPUNV',
    'HPBUFFERARB',
    'HPBUFFEREXT',
    'HPGPUNV',
    'HPVIDEODEV',
    'HVIDEOOUTPUTDEVICENV',
    'PFNWGLALLOCATEMEMORYNVPROC',
    'PFNWGLASSOCIATEIMAGEBUFFEREVENTSI3DPROC',
    'PFNWGLBEGINFRAMETRACKINGI3DPROC',
    'PFNWGLBINDDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLBINDSWAPBARRIERNVPROC',
    'PFNWGLBINDTEXIMAGEARBPROC',
    'PFNWGLBINDVIDEODEVICENVPROC',
    'PFNWGLBINDVIDEOIMAGENVPROC',
    'PFNWGLCHOOSEPIXELFORMATARBPROC',
    'PFNWGLCHOOSEPIXELFORMATEXTPROC',
    'PFNWGLCREATEAFFINITYDCNVPROC',
    'PFNWGLCREATEBUFFERREGIONARBPROC',
    'PFNWGLCREATECONTEXTATTRIBSARBPROC',
    'PFNWGLCREATEDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLCREATEIMAGEBUFFERI3DPROC',
    'PFNWGLCREATEPBUFFERARBPROC',
    'PFNWGLCREATEPBUFFEREXTPROC',
    'PFNWGLDELETEBUFFERREGIONARBPROC',
    'PFNWGLDELETEDCNVPROC',
    'PFNWGLDESTROYDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLDESTROYIMAGEBUFFERI3DPROC',
    'PFNWGLDESTROYPBUFFERARBPROC',
    'PFNWGLDESTROYPBUFFEREXTPROC',
    'PFNWGLDISABLEFRAMELOCKI3DPROC',
    'PFNWGLDISABLEGENLOCKI3DPROC',
    'PFNWGLENABLEFRAMELOCKI3DPROC',
    'PFNWGLENABLEGENLOCKI3DPROC',
    'PFNWGLENDFRAMETRACKINGI3DPROC',
    'PFNWGLENUMERATEVIDEODEVICESNVPROC',
    'PFNWGLENUMGPUDEVICESNVPROC',
    'PFNWGLENUMGPUSFROMAFFINITYDCNVPROC',
    'PFNWGLENUMGPUSNVPROC',
    'PFNWGLFREEMEMORYNVPROC',
    'PFNWGLGENLOCKSAMPLERATEI3DPROC',
    'PFNWGLGENLOCKSOURCEDELAYI3DPROC',
    'PFNWGLGENLOCKSOURCEEDGEI3DPROC',
    'PFNWGLGENLOCKSOURCEI3DPROC',
    'PFNWGLGETCURRENTREADDCARBPROC',
    'PFNWGLGETCURRENTREADDCEXTPROC',
    'PFNWGLGETDIGITALVIDEOPARAMETERSI3DPROC',
    'PFNWGLGETEXTENSIONSSTRINGARBPROC',
    'PFNWGLGETEXTENSIONSSTRINGEXTPROC',
    'PFNWGLGETFRAMEUSAGEI3DPROC',
    'PFNWGLGETGAMMATABLEI3DPROC',
    'PFNWGLGETGAMMATABLEPARAMETERSI3DPROC',
    'PFNWGLGETGENLOCKSAMPLERATEI3DPROC',
    'PFNWGLGETGENLOCKSOURCEDELAYI3DPROC',
    'PFNWGLGETGENLOCKSOURCEEDGEI3DPROC',
    'PFNWGLGETGENLOCKSOURCEI3DPROC',
    'PFNWGLGETMSCRATEOMLPROC',
    'PFNWGLGETPBUFFERDCARBPROC',
    'PFNWGLGETPBUFFERDCEXTPROC',
    'PFNWGLGETPIXELFORMATATTRIBFVARBPROC',
    'PFNWGLGETPIXELFORMATATTRIBFVEXTPROC',
    'PFNWGLGETPIXELFORMATATTRIBIVARBPROC',
    'PFNWGLGETPIXELFORMATATTRIBIVEXTPROC',
    'PFNWGLGETSWAPINTERVALEXTPROC',
    'PFNWGLGETSYNCVALUESOMLPROC',
    'PFNWGLGETVIDEODEVICENVPROC',
    'PFNWGLGETVIDEOINFONVPROC',
    'PFNWGLISENABLEDFRAMELOCKI3DPROC',
    'PFNWGLISENABLEDGENLOCKI3DPROC',
    'PFNWGLJOINSWAPGROUPNVPROC',
    'PFNWGLLOADDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLMAKECONTEXTCURRENTARBPROC',
    'PFNWGLMAKECONTEXTCURRENTEXTPROC',
    'PFNWGLQUERYCURRENTCONTEXTNVPROC',
    'PFNWGLQUERYFRAMECOUNTNVPROC',
    'PFNWGLQUERYFRAMELOCKMASTERI3DPROC',
    'PFNWGLQUERYFRAMETRACKINGI3DPROC',
    'PFNWGLQUERYGENLOCKMAXSOURCEDELAYI3DPROC',
    'PFNWGLQUERYMAXSWAPGROUPSNVPROC',
    'PFNWGLQUERYPBUFFERARBPROC',
    'PFNWGLQUERYPBUFFEREXTPROC',
    'PFNWGLQUERYSWAPGROUPNVPROC',
    'PFNWGLRELEASEIMAGEBUFFEREVENTSI3DPROC',
    'PFNWGLRELEASEPBUFFERDCARBPROC',
    'PFNWGLRELEASEPBUFFERDCEXTPROC',
    'PFNWGLRELEASETEXIMAGEARBPROC',
    'PFNWGLRELEASEVIDEODEVICENVPROC',
    'PFNWGLRELEASEVIDEOIMAGENVPROC',
    'PFNWGLRESETFRAMECOUNTNVPROC',
    'PFNWGLRESTOREBUFFERREGIONARBPROC',
    'PFNWGLSAVEBUFFERREGIONARBPROC',
    'PFNWGLSENDPBUFFERTOVIDEONVPROC',
    'PFNWGLSETDIGITALVIDEOPARAMETERSI3DPROC',
    'PFNWGLSETGAMMATABLEI3DPROC',
    'PFNWGLSETGAMMATABLEPARAMETERSI3DPROC',
    'PFNWGLSETPBUFFERATTRIBARBPROC',
    'PFNWGLSWAPBUFFERSMSCOMLPROC',
    'PFNWGLSWAPINTERVALEXTPROC',
    'PFNWGLSWAPLAYERBUFFERSMSCOMLPROC',
    'PFNWGLWAITFORMSCOMLPROC',
    'PFNWGLWAITFORSBCOMLPROC',
    'PGPU_DEVICE',
    'WGL_ACCELERATION_ARB',
    'WGL_ACCELERATION_EXT',
    'WGL_ACCUM_ALPHA_BITS_ARB',
    'WGL_ACCUM_ALPHA_BITS_EXT',
    'WGL_ACCUM_BITS_ARB',
    'WGL_ACCUM_BITS_EXT',
    'WGL_ACCUM_BLUE_BITS_ARB',
    'WGL_ACCUM_BLUE_BITS_EXT',
    'WGL_ACCUM_GREEN_BITS_ARB',
    'WGL_ACCUM_GREEN_BITS_EXT',
    'WGL_ACCUM_RED_BITS_ARB',
    'WGL_ACCUM_RED_BITS_EXT',
    'WGL_ALPHA_BITS_ARB',
    'WGL_ALPHA_BITS_EXT',
    'WGL_ALPHA_SHIFT_ARB',
    'WGL_ALPHA_SHIFT_EXT',
    'WGL_AUX0_ARB',
    'WGL_AUX1_ARB',
    'WGL_AUX2_ARB',
    'WGL_AUX3_ARB',
    'WGL_AUX4_ARB',
    'WGL_AUX5_ARB',
    'WGL_AUX6_ARB',
    'WGL_AUX7_ARB',
    'WGL_AUX8_ARB',
    'WGL_AUX9_ARB',
    'WGL_AUX_BUFFERS_ARB',
    'WGL_AUX_BUFFERS_EXT',
    'WGL_BACK_COLOR_BUFFER_BIT_ARB',
    'WGL_BACK_LEFT_ARB',
    'WGL_BACK_RIGHT_ARB',
    'WGL_BIND_TO_TEXTURE_DEPTH_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGBA_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGB_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RG_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_R_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_RGBA_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_RGB_NV',
    'WGL_BIND_TO_TEXTURE_RGBA_ARB',
    'WGL_BIND_TO_TEXTURE_RGB_ARB',
    'WGL_BIND_TO_VIDEO_RGBA_NV',
    'WGL_BIND_TO_VIDEO_RGB_AND_DEPTH_NV',
    'WGL_BIND_TO_VIDEO_RGB_NV',
    'WGL_BLUE_BITS_ARB',
    'WGL_BLUE_BITS_EXT',
    'WGL_BLUE_SHIFT_ARB',
    'WGL_BLUE_SHIFT_EXT',
    'WGL_COLOR_BITS_ARB',
    'WGL_COLOR_BITS_EXT',
    'WGL_CONTEXT_DEBUG_BIT_ARB',
    'WGL_CONTEXT_FLAGS_ARB',
    'WGL_CONTEXT_FORWARD_COMPATIBLE_BIT_ARB',
    'WGL_CONTEXT_LAYER_PLANE_ARB',
    'WGL_CONTEXT_MAJOR_VERSION_ARB',
    'WGL_CONTEXT_MINOR_VERSION_ARB',
    'WGL_CUBE_MAP_FACE_ARB',
    'WGL_DEPTH_BITS_ARB',
    'WGL_DEPTH_BITS_EXT',
    'WGL_DEPTH_BUFFER_BIT_ARB',
    'WGL_DEPTH_COMPONENT_NV',
    'WGL_DEPTH_FLOAT_EXT',
    'WGL_DEPTH_TEXTURE_FORMAT_NV',
    'WGL_DIGITAL_VIDEO_CURSOR_ALPHA_FRAMEBUFFER_I3D',
    'WGL_DIGITAL_VIDEO_CURSOR_ALPHA_VALUE_I3D',
    'WGL_DIGITAL_VIDEO_CURSOR_INCLUDED_I3D',
    'WGL_DIGITAL_VIDEO_GAMMA_CORRECTED_I3D',
    'WGL_DOUBLE_BUFFER_ARB',
    'WGL_DOUBLE_BUFFER_EXT',
    'WGL_DRAW_TO_BITMAP_ARB',
    'WGL_DRAW_TO_BITMAP_EXT',
    'WGL_DRAW_TO_PBUFFER_ARB',
    'WGL_DRAW_TO_PBUFFER_EXT',
    'WGL_DRAW_TO_WINDOW_ARB',
    'WGL_DRAW_TO_WINDOW_EXT',
    'WGL_ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV',
    'WGL_ERROR_MISSING_AFFINITY_MASK_NV',
    'WGL_FLOAT_COMPONENTS_NV',
    'WGL_FRAMEBUFFER_SRGB_CAPABLE_EXT',
    'WGL_FRONT_COLOR_BUFFER_BIT_ARB',
    'WGL_FRONT_LEFT_ARB',
    'WGL_FRONT_RIGHT_ARB',
    'WGL_FULL_ACCELERATION_ARB',
    'WGL_FULL_ACCELERATION_EXT',
    'WGL_GAMMA_EXCLUDE_DESKTOP_I3D',
    'WGL_GAMMA_TABLE_SIZE_I3D',
    'WGL_GENERIC_ACCELERATION_ARB',
    'WGL_GENERIC_ACCELERATION_EXT',
    'WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D',
    'WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_RISING_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_FIELD_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_SYNC_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_TTL_I3D',
    'WGL_GENLOCK_SOURCE_MULTIVIEW_I3D',
    'WGL_GREEN_BITS_ARB',
    'WGL_GREEN_BITS_EXT',
    'WGL_GREEN_SHIFT_ARB',
    'WGL_GREEN_SHIFT_EXT',
    'WGL_IMAGE_BUFFER_LOCK_I3D',
    'WGL_IMAGE_BUFFER_MIN_ACCESS_I3D',
    'WGL_MAX_PBUFFER_HEIGHT_ARB',
    'WGL_MAX_PBUFFER_HEIGHT_EXT',
    'WGL_MAX_PBUFFER_PIXELS_ARB',
    'WGL_MAX_PBUFFER_PIXELS_EXT',
    'WGL_MAX_PBUFFER_WIDTH_ARB',
    'WGL_MAX_PBUFFER_WIDTH_EXT',
    'WGL_MIPMAP_LEVEL_ARB',
    'WGL_MIPMAP_TEXTURE_ARB',
    'WGL_NEED_PALETTE_ARB',
    'WGL_NEED_PALETTE_EXT',
    'WGL_NEED_SYSTEM_PALETTE_ARB',
    'WGL_NEED_SYSTEM_PALETTE_EXT',
    'WGL_NO_ACCELERATION_ARB',
    'WGL_NO_ACCELERATION_EXT',
    'WGL_NO_TEXTURE_ARB',
    'WGL_NUMBER_OVERLAYS_ARB',
    'WGL_NUMBER_OVERLAYS_EXT',
    'WGL_NUMBER_PIXEL_FORMATS_ARB',
    'WGL_NUMBER_PIXEL_FORMATS_EXT',
    'WGL_NUMBER_UNDERLAYS_ARB',
    'WGL_NUMBER_UNDERLAYS_EXT',
    'WGL_NUM_VIDEO_SLOTS_NV',
    'WGL_OPTIMAL_PBUFFER_HEIGHT_EXT',
    'WGL_OPTIMAL_PBUFFER_WIDTH_EXT',
    'WGL_PBUFFER_HEIGHT_ARB',
    'WGL_PBUFFER_HEIGHT_EXT',
    'WGL_PBUFFER_LARGEST_ARB',
    'WGL_PBUFFER_LARGEST_EXT',
    'WGL_PBUFFER_LOST_ARB',
    'WGL_PBUFFER_WIDTH_ARB',
    'WGL_PBUFFER_WIDTH_EXT',
    'WGL_PIXEL_TYPE_ARB',
    'WGL_PIXEL_TYPE_EXT',
    'WGL_RED_BITS_ARB',
    'WGL_RED_BITS_EXT',
    'WGL_RED_SHIFT_ARB',
    'WGL_RED_SHIFT_EXT',
    'WGL_SAMPLES_3DFX',
    'WGL_SAMPLES_ARB',
    'WGL_SAMPLES_EXT',
    'WGL_SAMPLE_BUFFERS_3DFX',
    'WGL_SAMPLE_BUFFERS_ARB',
    'WGL_SAMPLE_BUFFERS_EXT',
    'WGL_SHARE_ACCUM_ARB',
    'WGL_SHARE_ACCUM_EXT',
    'WGL_SHARE_DEPTH_ARB',
    'WGL_SHARE_DEPTH_EXT',
    'WGL_SHARE_STENCIL_ARB',
    'WGL_SHARE_STENCIL_EXT',
    'WGL_STENCIL_BITS_ARB',
    'WGL_STENCIL_BITS_EXT',
    'WGL_STENCIL_BUFFER_BIT_ARB',
    'WGL_STEREO_ARB',
    'WGL_STEREO_EMITTER_DISABLE_3DL',
    'WGL_STEREO_EMITTER_ENABLE_3DL',
    'WGL_STEREO_EXT',
    'WGL_STEREO_POLARITY_INVERT_3DL',
    'WGL_STEREO_POLARITY_NORMAL_3DL',
    'WGL_SUPPORT_GDI_ARB',
    'WGL_SUPPORT_GDI_EXT',
    'WGL_SUPPORT_OPENGL_ARB',
    'WGL_SUPPORT_OPENGL_EXT',
    'WGL_SWAP_COPY_ARB',
    'WGL_SWAP_COPY_EXT',
    'WGL_SWAP_EXCHANGE_ARB',
    'WGL_SWAP_EXCHANGE_EXT',
    'WGL_SWAP_LAYER_BUFFERS_ARB',
    'WGL_SWAP_LAYER_BUFFERS_EXT',
    'WGL_SWAP_METHOD_ARB',
    'WGL_SWAP_METHOD_EXT',
    'WGL_SWAP_UNDEFINED_ARB',
    'WGL_SWAP_UNDEFINED_EXT',
    'WGL_TEXTURE_1D_ARB',
    'WGL_TEXTURE_2D_ARB',
    'WGL_TEXTURE_CUBE_MAP_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB',
    'WGL_TEXTURE_DEPTH_COMPONENT_NV',
    'WGL_TEXTURE_FLOAT_RGBA_NV',
    'WGL_TEXTURE_FLOAT_RGB_NV',
    'WGL_TEXTURE_FLOAT_RG_NV',
    'WGL_TEXTURE_FLOAT_R_NV',
    'WGL_TEXTURE_FORMAT_ARB',
    'WGL_TEXTURE_RECTANGLE_NV',
    'WGL_TEXTURE_RGBA_ARB',
    'WGL_TEXTURE_RGB_ARB',
    'WGL_TEXTURE_TARGET_ARB',
    'WGL_TRANSPARENT_ALPHA_VALUE_ARB',
    'WGL_TRANSPARENT_ARB',
    'WGL_TRANSPARENT_BLUE_VALUE_ARB',
    'WGL_TRANSPARENT_EXT',
    'WGL_TRANSPARENT_GREEN_VALUE_ARB',
    'WGL_TRANSPARENT_INDEX_VALUE_ARB',
    'WGL_TRANSPARENT_RED_VALUE_ARB',
    'WGL_TRANSPARENT_VALUE_EXT',
    'WGL_TYPE_COLORINDEX_ARB',
    'WGL_TYPE_COLORINDEX_EXT',
    'WGL_TYPE_RGBA_ARB',
    'WGL_TYPE_RGBA_EXT',
    'WGL_TYPE_RGBA_FLOAT_ARB',
    'WGL_TYPE_RGBA_FLOAT_ATI',
    'WGL_TYPE_RGBA_UNSIGNED_FLOAT_EXT',
    'WGL_VIDEO_OUT_ALPHA_NV',
    'WGL_VIDEO_OUT_COLOR_AND_ALPHA_NV',
    'WGL_VIDEO_OUT_COLOR_AND_DEPTH_NV',
    'WGL_VIDEO_OUT_COLOR_NV',
    'WGL_VIDEO_OUT_DEPTH_NV',
    'WGL_VIDEO_OUT_FIELD_1',
    'WGL_VIDEO_OUT_FIELD_2',
    'WGL_VIDEO_OUT_FRAME',
    'WGL_VIDEO_OUT_STACKED_FIELDS_1_2',
    'WGL_VIDEO_OUT_STACKED_FIELDS_2_1',
    'WGL_WGLEXT_VERSION',
    'WIN32_LEAN_AND_MEAN',
    'WGL_3DFX_multisample',
    'WGL_ARB_buffer_region',
    'WGL_ARB_create_context',
    'WGL_ARB_extensions_string',
    'WGL_ARB_make_current_read',
    'WGL_ARB_multisample',
    'WGL_ARB_pbuffer',
    'WGL_ARB_pixel_format',
    'WGL_ARB_pixel_format_float',
    'WGL_ARB_render_texture',
    'WGL_ATI_pixel_format_float',
    'WGL_EXT_depth_float',
    'WGL_EXT_display_color_table',
    'WGL_EXT_extensions_string',
    'WGL_EXT_framebuffer_sRGB',
    'WGL_EXT_make_current_read',
    'WGL_EXT_multisample',
    'WGL_EXT_pbuffer',
    'WGL_EXT_pixel_format',
    'WGL_EXT_pixel_format_packed_float',
    'WGL_EXT_swap_control',
    'WGL_I3D_digital_video_control',
    'WGL_I3D_gamma',
    'WGL_I3D_genlock',
    'WGL_I3D_image_buffer',
    'WGL_I3D_swap_frame_lock',
    'WGL_I3D_swap_frame_usage',
    'WGL_NV_float_buffer',
    'WGL_NV_gpu_affinity',
    'WGL_NV_present_video',
    'WGL_NV_swap_group',
    'WGL_NV_vertex_array_range',
    'WGL_NV_video_out',
    'WGL_OML_sync_control',
    'wglAllocateMemoryNV',
    'wglAssociateImageBufferEventsI3D',
    'wglBeginFrameTrackingI3D',
    'wglBindDisplayColorTableEXT',
    'wglBindSwapBarrierNV',
    'wglBindTexImageARB',
    'wglBindVideoDeviceNV',
    'wglBindVideoImageNV',
    'wglChoosePixelFormatARB',
    'wglChoosePixelFormatEXT',
    'wglCreateAffinityDCNV',
    'wglCreateBufferRegionARB',
    'wglCreateContextAttribsARB',
    'wglCreateDisplayColorTableEXT',
    'wglCreateImageBufferI3D',
    'wglCreatePbufferARB',
    'wglCreatePbufferEXT',
    'wglDeleteBufferRegionARB',
    'wglDeleteDCNV',
    'wglDestroyDisplayColorTableEXT',
    'wglDestroyImageBufferI3D',
    'wglDestroyPbufferARB',
    'wglDestroyPbufferEXT',
    'wglDisableFrameLockI3D',
    'wglDisableGenlockI3D',
    'wglEnableFrameLockI3D',
    'wglEnableGenlockI3D',
    'wglEndFrameTrackingI3D',
    'wglEnumGpuDevicesNV',
    'wglEnumGpusFromAffinityDCNV',
    'wglEnumGpusNV',
    'wglEnumerateVideoDevicesNV',
    'wglFreeMemoryNV',
    'wglGenlockSampleRateI3D',
    'wglGenlockSourceDelayI3D',
    'wglGenlockSourceEdgeI3D',
    'wglGenlockSourceI3D',
    'wglGetCurrentReadDCARB',
    'wglGetCurrentReadDCEXT',
    'wglGetDigitalVideoParametersI3D',
    'wglGetExtensionsStringARB',
    'wglGetExtensionsStringEXT',
    'wglGetFrameUsageI3D',
    'wglGetGammaTableI3D',
    'wglGetGammaTableParametersI3D',
    'wglGetGenlockSampleRateI3D',
    'wglGetGenlockSourceDelayI3D',
    'wglGetGenlockSourceEdgeI3D',
    'wglGetGenlockSourceI3D',
    'wglGetMscRateOML',
    'wglGetPbufferDCARB',
    'wglGetPbufferDCEXT',
    'wglGetPixelFormatAttribfvARB',
    'wglGetPixelFormatAttribfvEXT',
    'wglGetPixelFormatAttribivARB',
    'wglGetPixelFormatAttribivEXT',
    'wglGetSwapIntervalEXT',
    'wglGetSyncValuesOML',
    'wglGetVideoDeviceNV',
    'wglGetVideoInfoNV',
    'wglIsEnabledFrameLockI3D',
    'wglIsEnabledGenlockI3D',
    'wglJoinSwapGroupNV',
    'wglLoadDisplayColorTableEXT',
    'wglMakeContextCurrentARB',
    'wglMakeContextCurrentEXT',
    'wglQueryCurrentContextNV',
    'wglQueryFrameCountNV',
    'wglQueryFrameLockMasterI3D',
    'wglQueryFrameTrackingI3D',
    'wglQueryGenlockMaxSourceDelayI3D',
    'wglQueryMaxSwapGroupsNV',
    'wglQueryPbufferARB',
    'wglQueryPbufferEXT',
    'wglQuerySwapGroupNV',
    'wglReleaseImageBufferEventsI3D',
    'wglReleasePbufferDCARB',
    'wglReleasePbufferDCEXT',
    'wglReleaseTexImageARB',
    'wglReleaseVideoDeviceNV',
    'wglReleaseVideoImageNV',
    'wglResetFrameCountNV',
    'wglRestoreBufferRegionARB',
    'wglSaveBufferRegionARB',
    'wglSendPbufferToVideoNV',
    'wglSetDigitalVideoParametersI3D',
    'wglSetGammaTableI3D',
    'wglSetGammaTableParametersI3D',
    'wglSetPbufferAttribARB',
    'wglSwapBuffersMscOML',
    'wglSwapIntervalEXT',
    'wglSwapLayerBuffersMscOML',
    'wglWaitForMscOML',
    'wglWaitForSbcOML',
]
# END GENERATED CONTENT (do not edit above this line)



