"""Wrapper for http://developer.download.nvidia.com/opengl/includes/wglext.h

Generated by tools/gengl.py.
Do not modify this file.
"""

from ctypes import CFUNCTYPE, POINTER, Structure, c_char_p, c_float, c_int, c_ubyte, c_uint, c_ushort
from ctypes.wintypes import CHAR, FLOAT, LPVOID
from pyglet.gl.lib import link_WGL as _link_function
from pyglet.gl.lib import c_void
from pyglet.libs.win32.types import VOID, INT64, LONG, HANDLE, DWORD, UINT, BOOL, HDC

# BEGIN GENERATED CONTENT (do not edit below this line)

# This content is generated by tools/gengl.py.
# Wrapper for http://developer.download.nvidia.com/opengl/includes/wglext.h


# H (C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:7)
# H (C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:7)
WIN32_LEAN_AND_MEAN = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:40
WGL_WGLEXT_VERSION = 6 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:60
# ARB_buffer_region (http://developer.download.nvidia.com/opengl/includes/wglext.h:62)
WGL_FRONT_COLOR_BUFFER_BIT_ARB = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:63
WGL_BACK_COLOR_BUFFER_BIT_ARB = 2 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:64
WGL_DEPTH_BUFFER_BIT_ARB = 4 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:65
WGL_STENCIL_BUFFER_BIT_ARB = 8 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:66
# ARB_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:69)
WGL_SAMPLE_BUFFERS_ARB = 8257 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:70
WGL_SAMPLES_ARB = 8258 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:71
# ARB_extensions_string (http://developer.download.nvidia.com/opengl/includes/wglext.h:74)
# ARB_pixel_format (http://developer.download.nvidia.com/opengl/includes/wglext.h:77)
WGL_NUMBER_PIXEL_FORMATS_ARB = 8192 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:78
WGL_DRAW_TO_WINDOW_ARB = 8193 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:79
WGL_DRAW_TO_BITMAP_ARB = 8194 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:80
WGL_ACCELERATION_ARB = 8195 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:81
WGL_NEED_PALETTE_ARB = 8196 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:82
WGL_NEED_SYSTEM_PALETTE_ARB = 8197 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:83
WGL_SWAP_LAYER_BUFFERS_ARB = 8198 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:84
WGL_SWAP_METHOD_ARB = 8199 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:85
WGL_NUMBER_OVERLAYS_ARB = 8200 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:86
WGL_NUMBER_UNDERLAYS_ARB = 8201 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:87
WGL_TRANSPARENT_ARB = 8202 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:88
WGL_TRANSPARENT_RED_VALUE_ARB = 8247 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:89
WGL_TRANSPARENT_GREEN_VALUE_ARB = 8248 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:90
WGL_TRANSPARENT_BLUE_VALUE_ARB = 8249 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:91
WGL_TRANSPARENT_ALPHA_VALUE_ARB = 8250 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:92
WGL_TRANSPARENT_INDEX_VALUE_ARB = 8251 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:93
WGL_SHARE_DEPTH_ARB = 8204 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:94
WGL_SHARE_STENCIL_ARB = 8205 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:95
WGL_SHARE_ACCUM_ARB = 8206 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:96
WGL_SUPPORT_GDI_ARB = 8207 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:97
WGL_SUPPORT_OPENGL_ARB = 8208 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:98
WGL_DOUBLE_BUFFER_ARB = 8209 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:99
WGL_STEREO_ARB = 8210 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:100
WGL_PIXEL_TYPE_ARB = 8211 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:101
WGL_COLOR_BITS_ARB = 8212 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:102
WGL_RED_BITS_ARB = 8213 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:103
WGL_RED_SHIFT_ARB = 8214 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:104
WGL_GREEN_BITS_ARB = 8215 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:105
WGL_GREEN_SHIFT_ARB = 8216 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:106
WGL_BLUE_BITS_ARB = 8217 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:107
WGL_BLUE_SHIFT_ARB = 8218 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:108
WGL_ALPHA_BITS_ARB = 8219 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:109
WGL_ALPHA_SHIFT_ARB = 8220 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:110
WGL_ACCUM_BITS_ARB = 8221 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:111
WGL_ACCUM_RED_BITS_ARB = 8222 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:112
WGL_ACCUM_GREEN_BITS_ARB = 8223 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:113
WGL_ACCUM_BLUE_BITS_ARB = 8224 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:114
WGL_ACCUM_ALPHA_BITS_ARB = 8225 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:115
WGL_DEPTH_BITS_ARB = 8226 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:116
WGL_STENCIL_BITS_ARB = 8227 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:117
WGL_AUX_BUFFERS_ARB = 8228 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:118
WGL_NO_ACCELERATION_ARB = 8229 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:119
WGL_GENERIC_ACCELERATION_ARB = 8230 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:120
WGL_FULL_ACCELERATION_ARB = 8231 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:121
WGL_SWAP_EXCHANGE_ARB = 8232 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:122
WGL_SWAP_COPY_ARB = 8233 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:123
WGL_SWAP_UNDEFINED_ARB = 8234 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:124
WGL_TYPE_RGBA_ARB = 8235 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:125
WGL_TYPE_COLORINDEX_ARB = 8236 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:126
# ARB_make_current_read (http://developer.download.nvidia.com/opengl/includes/wglext.h:129)
ERROR_INVALID_PIXEL_TYPE_ARB = 8259 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:130
ERROR_INCOMPATIBLE_DEVICE_CONTEXTS_ARB = 8276 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:131
# ARB_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:134)
WGL_DRAW_TO_PBUFFER_ARB = 8237 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:135
WGL_MAX_PBUFFER_PIXELS_ARB = 8238 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:136
WGL_MAX_PBUFFER_WIDTH_ARB = 8239 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:137
WGL_MAX_PBUFFER_HEIGHT_ARB = 8240 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:138
WGL_PBUFFER_LARGEST_ARB = 8243 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:139
WGL_PBUFFER_WIDTH_ARB = 8244 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:140
WGL_PBUFFER_HEIGHT_ARB = 8245 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:141
WGL_PBUFFER_LOST_ARB = 8246 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:142
# ARB_render_texture (http://developer.download.nvidia.com/opengl/includes/wglext.h:145)
WGL_BIND_TO_TEXTURE_RGB_ARB = 8304 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:146
WGL_BIND_TO_TEXTURE_RGBA_ARB = 8305 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:147
WGL_TEXTURE_FORMAT_ARB = 8306 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:148
WGL_TEXTURE_TARGET_ARB = 8307 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:149
WGL_MIPMAP_TEXTURE_ARB = 8308 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:150
WGL_TEXTURE_RGB_ARB = 8309 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:151
WGL_TEXTURE_RGBA_ARB = 8310 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:152
WGL_NO_TEXTURE_ARB = 8311 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:153
WGL_TEXTURE_CUBE_MAP_ARB = 8312 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:154
WGL_TEXTURE_1D_ARB = 8313 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:155
WGL_TEXTURE_2D_ARB = 8314 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:156
WGL_MIPMAP_LEVEL_ARB = 8315 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:157
WGL_CUBE_MAP_FACE_ARB = 8316 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:158
WGL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB = 8317 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:159
WGL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB = 8318 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:160
WGL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB = 8319 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:161
WGL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB = 8320 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:162
WGL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB = 8321 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:163
WGL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB = 8322 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:164
WGL_FRONT_LEFT_ARB = 8323 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:165
WGL_FRONT_RIGHT_ARB = 8324 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:166
WGL_BACK_LEFT_ARB = 8325 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:167
WGL_BACK_RIGHT_ARB = 8326 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:168
WGL_AUX0_ARB = 8327 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:169
WGL_AUX1_ARB = 8328 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:170
WGL_AUX2_ARB = 8329 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:171
WGL_AUX3_ARB = 8330 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:172
WGL_AUX4_ARB = 8331 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:173
WGL_AUX5_ARB = 8332 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:174
WGL_AUX6_ARB = 8333 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:175
WGL_AUX7_ARB = 8334 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:176
WGL_AUX8_ARB = 8335 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:177
WGL_AUX9_ARB = 8336 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:178
# ARB_pixel_format_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:181)
WGL_TYPE_RGBA_FLOAT_ARB = 8608 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:182
# EXT_make_current_read (http://developer.download.nvidia.com/opengl/includes/wglext.h:185)
ERROR_INVALID_PIXEL_TYPE_EXT = 8259 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:186
# EXT_pixel_format (http://developer.download.nvidia.com/opengl/includes/wglext.h:189)
WGL_NUMBER_PIXEL_FORMATS_EXT = 8192 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:190
WGL_DRAW_TO_WINDOW_EXT = 8193 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:191
WGL_DRAW_TO_BITMAP_EXT = 8194 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:192
WGL_ACCELERATION_EXT = 8195 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:193
WGL_NEED_PALETTE_EXT = 8196 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:194
WGL_NEED_SYSTEM_PALETTE_EXT = 8197 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:195
WGL_SWAP_LAYER_BUFFERS_EXT = 8198 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:196
WGL_SWAP_METHOD_EXT = 8199 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:197
WGL_NUMBER_OVERLAYS_EXT = 8200 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:198
WGL_NUMBER_UNDERLAYS_EXT = 8201 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:199
WGL_TRANSPARENT_EXT = 8202 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:200
WGL_TRANSPARENT_VALUE_EXT = 8203 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:201
WGL_SHARE_DEPTH_EXT = 8204 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:202
WGL_SHARE_STENCIL_EXT = 8205 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:203
WGL_SHARE_ACCUM_EXT = 8206 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:204
WGL_SUPPORT_GDI_EXT = 8207 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:205
WGL_SUPPORT_OPENGL_EXT = 8208 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:206
WGL_DOUBLE_BUFFER_EXT = 8209 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:207
WGL_STEREO_EXT = 8210 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:208
WGL_PIXEL_TYPE_EXT = 8211 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:209
WGL_COLOR_BITS_EXT = 8212 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:210
WGL_RED_BITS_EXT = 8213 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:211
WGL_RED_SHIFT_EXT = 8214 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:212
WGL_GREEN_BITS_EXT = 8215 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:213
WGL_GREEN_SHIFT_EXT = 8216 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:214
WGL_BLUE_BITS_EXT = 8217 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:215
WGL_BLUE_SHIFT_EXT = 8218 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:216
WGL_ALPHA_BITS_EXT = 8219 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:217
WGL_ALPHA_SHIFT_EXT = 8220 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:218
WGL_ACCUM_BITS_EXT = 8221 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:219
WGL_ACCUM_RED_BITS_EXT = 8222 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:220
WGL_ACCUM_GREEN_BITS_EXT = 8223 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:221
WGL_ACCUM_BLUE_BITS_EXT = 8224 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:222
WGL_ACCUM_ALPHA_BITS_EXT = 8225 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:223
WGL_DEPTH_BITS_EXT = 8226 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:224
WGL_STENCIL_BITS_EXT = 8227 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:225
WGL_AUX_BUFFERS_EXT = 8228 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:226
WGL_NO_ACCELERATION_EXT = 8229 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:227
WGL_GENERIC_ACCELERATION_EXT = 8230 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:228
WGL_FULL_ACCELERATION_EXT = 8231 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:229
WGL_SWAP_EXCHANGE_EXT = 8232 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:230
WGL_SWAP_COPY_EXT = 8233 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:231
WGL_SWAP_UNDEFINED_EXT = 8234 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:232
WGL_TYPE_RGBA_EXT = 8235 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:233
WGL_TYPE_COLORINDEX_EXT = 8236 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:234
# EXT_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:237)
WGL_DRAW_TO_PBUFFER_EXT = 8237 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:238
WGL_MAX_PBUFFER_PIXELS_EXT = 8238 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:239
WGL_MAX_PBUFFER_WIDTH_EXT = 8239 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:240
WGL_MAX_PBUFFER_HEIGHT_EXT = 8240 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:241
WGL_OPTIMAL_PBUFFER_WIDTH_EXT = 8241 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:242
WGL_OPTIMAL_PBUFFER_HEIGHT_EXT = 8242 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:243
WGL_PBUFFER_LARGEST_EXT = 8243 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:244
WGL_PBUFFER_WIDTH_EXT = 8244 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:245
WGL_PBUFFER_HEIGHT_EXT = 8245 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:246
# EXT_depth_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:249)
WGL_DEPTH_FLOAT_EXT = 8256 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:250
# 3DFX_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:253)
WGL_SAMPLE_BUFFERS_3DFX = 8288 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:254
WGL_SAMPLES_3DFX = 8289 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:255
# EXT_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:258)
WGL_SAMPLE_BUFFERS_EXT = 8257 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:259
WGL_SAMPLES_EXT = 8258 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:260
# I3D_digital_video_control (http://developer.download.nvidia.com/opengl/includes/wglext.h:263)
WGL_DIGITAL_VIDEO_CURSOR_ALPHA_FRAMEBUFFER_I3D = 8272 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:264
WGL_DIGITAL_VIDEO_CURSOR_ALPHA_VALUE_I3D = 8273 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:265
WGL_DIGITAL_VIDEO_CURSOR_INCLUDED_I3D = 8274 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:266
WGL_DIGITAL_VIDEO_GAMMA_CORRECTED_I3D = 8275 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:267
# I3D_gamma (http://developer.download.nvidia.com/opengl/includes/wglext.h:270)
WGL_GAMMA_TABLE_SIZE_I3D = 8270 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:271
WGL_GAMMA_EXCLUDE_DESKTOP_I3D = 8271 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:272
# I3D_genlock (http://developer.download.nvidia.com/opengl/includes/wglext.h:275)
WGL_GENLOCK_SOURCE_MULTIVIEW_I3D = 8260 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:276
WGL_GENLOCK_SOURCE_EXTENAL_SYNC_I3D = 8261 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:277
WGL_GENLOCK_SOURCE_EXTENAL_FIELD_I3D = 8262 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:278
WGL_GENLOCK_SOURCE_EXTENAL_TTL_I3D = 8263 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:279
WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D = 8264 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:280
WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D = 8265 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:281
WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D = 8266 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:282
WGL_GENLOCK_SOURCE_EDGE_RISING_I3D = 8267 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:283
WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D = 8268 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:284
# I3D_image_buffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:287)
WGL_IMAGE_BUFFER_MIN_ACCESS_I3D = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:288
WGL_IMAGE_BUFFER_LOCK_I3D = 2 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:289
# I3D_swap_frame_lock (http://developer.download.nvidia.com/opengl/includes/wglext.h:292)
# NV_render_depth_texture (http://developer.download.nvidia.com/opengl/includes/wglext.h:295)
WGL_BIND_TO_TEXTURE_DEPTH_NV = 8355 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:296
WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV = 8356 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:297
WGL_DEPTH_TEXTURE_FORMAT_NV = 8357 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:298
WGL_TEXTURE_DEPTH_COMPONENT_NV = 8358 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:299
WGL_DEPTH_COMPONENT_NV = 8359 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:300
# NV_render_texture_rectangle (http://developer.download.nvidia.com/opengl/includes/wglext.h:303)
WGL_BIND_TO_TEXTURE_RECTANGLE_RGB_NV = 8352 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:304
WGL_BIND_TO_TEXTURE_RECTANGLE_RGBA_NV = 8353 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:305
WGL_TEXTURE_RECTANGLE_NV = 8354 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:306
# ATI_pixel_format_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:309)
WGL_TYPE_RGBA_FLOAT_ATI = 8608 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:310
WGL_RGBA_FLOAT_MODE_ATI = 34848 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:311
WGL_COLOR_CLEAR_UNCLAMPED_VALUE_ATI = 34869 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:312
# NV_float_buffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:315)
WGL_FLOAT_COMPONENTS_NV = 8368 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:316
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_R_NV = 8369 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:317
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RG_NV = 8370 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:318
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGB_NV = 8371 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:319
WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGBA_NV = 8372 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:320
WGL_TEXTURE_FLOAT_R_NV = 8373 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:321
WGL_TEXTURE_FLOAT_RG_NV = 8374 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:322
WGL_TEXTURE_FLOAT_RGB_NV = 8375 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:323
WGL_TEXTURE_FLOAT_RGBA_NV = 8376 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:324
# NV_swap_group (http://developer.download.nvidia.com/opengl/includes/wglext.h:327)
# NV_gpu_affinity (http://developer.download.nvidia.com/opengl/includes/wglext.h:330)
WGL_ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV = 8400 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:331
WGL_ERROR_MISSING_AFFINITY_MASK_NV = 8401 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:332
# ARB_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:338)

HPBUFFERARB = HANDLE 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:339
# EXT_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:341)
HPBUFFEREXT = HANDLE 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:342
# NV_gpu_affinity (http://developer.download.nvidia.com/opengl/includes/wglext.h:345)
HGPUNV = HANDLE 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:346
class struct__GPU_DEVICE(Structure):
    __slots__ = [
        'cb',
        'DeviceName',
        'DeviceString',
        'Flags',
        'rcVirtualScreen',
    ]

class struct_tagRECT(Structure):
    __slots__ = [
        'left',
        'top',
        'right',
        'bottom',
    ]

struct_tagRECT._fields_ = [
    ('left', LONG),
    ('top', LONG),
    ('right', LONG),
    ('bottom', LONG),
]

RECT = struct_tagRECT 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:200
struct__GPU_DEVICE._fields_ = [
    ('cb', DWORD),
    ('DeviceName', CHAR * 32),
    ('DeviceString', CHAR * 128),
    ('Flags', DWORD),
    ('rcVirtualScreen', RECT),
]

GPU_DEVICE = struct__GPU_DEVICE 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:353
PGPU_DEVICE = POINTER(struct__GPU_DEVICE) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:353
# ARB_buffer_region (http://developer.download.nvidia.com/opengl/includes/wglext.h:356)
WGL_ARB_buffer_region = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:357

# http://developer.download.nvidia.com/opengl/includes/wglext.h:359
wglCreateBufferRegionARB = _link_function('wglCreateBufferRegionARB', HANDLE, [HDC, c_int, UINT], 'ARB_buffer_region')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:360
wglDeleteBufferRegionARB = _link_function('wglDeleteBufferRegionARB', VOID, [HANDLE], 'ARB_buffer_region')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:361
wglSaveBufferRegionARB = _link_function('wglSaveBufferRegionARB', BOOL, [HANDLE, c_int, c_int, c_int, c_int], 'ARB_buffer_region')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:362
wglRestoreBufferRegionARB = _link_function('wglRestoreBufferRegionARB', BOOL, [HANDLE, c_int, c_int, c_int, c_int, c_int, c_int], 'ARB_buffer_region')

PFNWGLCREATEBUFFERREGIONARBPROC = CFUNCTYPE(HANDLE, HDC, c_int, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:364
PFNWGLDELETEBUFFERREGIONARBPROC = CFUNCTYPE(VOID, HANDLE) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:365
PFNWGLSAVEBUFFERREGIONARBPROC = CFUNCTYPE(BOOL, HANDLE, c_int, c_int, c_int, c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:366
PFNWGLRESTOREBUFFERREGIONARBPROC = CFUNCTYPE(BOOL, HANDLE, c_int, c_int, c_int, c_int, c_int, c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:367
# ARB_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:370)
WGL_ARB_multisample = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:371
# ARB_extensions_string (http://developer.download.nvidia.com/opengl/includes/wglext.h:374)
WGL_ARB_extensions_string = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:375
# http://developer.download.nvidia.com/opengl/includes/wglext.h:377
wglGetExtensionsStringARB = _link_function('wglGetExtensionsStringARB', c_char_p, [HDC], 'ARB_extensions_string')

PFNWGLGETEXTENSIONSSTRINGARBPROC = CFUNCTYPE(c_char_p, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:379
# ARB_pixel_format (http://developer.download.nvidia.com/opengl/includes/wglext.h:382)
WGL_ARB_pixel_format = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:383
# http://developer.download.nvidia.com/opengl/includes/wglext.h:385
wglGetPixelFormatAttribivARB = _link_function('wglGetPixelFormatAttribivARB', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)], 'ARB_pixel_format')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:386
wglGetPixelFormatAttribfvARB = _link_function('wglGetPixelFormatAttribfvARB', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)], 'ARB_pixel_format')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:387
wglChoosePixelFormatARB = _link_function('wglChoosePixelFormatARB', BOOL, [HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)], 'ARB_pixel_format')

PFNWGLGETPIXELFORMATATTRIBIVARBPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:389
PFNWGLGETPIXELFORMATATTRIBFVARBPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:390
PFNWGLCHOOSEPIXELFORMATARBPROC = CFUNCTYPE(BOOL, HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:391
# ARB_make_current_read (http://developer.download.nvidia.com/opengl/includes/wglext.h:394)
WGL_ARB_make_current_read = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:395
HGLRC = HANDLE 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:60
# http://developer.download.nvidia.com/opengl/includes/wglext.h:397
wglMakeContextCurrentARB = _link_function('wglMakeContextCurrentARB', BOOL, [HDC, HDC, HGLRC], 'ARB_make_current_read')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:398
wglGetCurrentReadDCARB = _link_function('wglGetCurrentReadDCARB', HDC, [], 'ARB_make_current_read')

PFNWGLMAKECONTEXTCURRENTARBPROC = CFUNCTYPE(BOOL, HDC, HDC, HGLRC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:400
PFNWGLGETCURRENTREADDCARBPROC = CFUNCTYPE(HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:401
# ARB_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:404)
WGL_ARB_pbuffer = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:405
# http://developer.download.nvidia.com/opengl/includes/wglext.h:407
wglCreatePbufferARB = _link_function('wglCreatePbufferARB', HPBUFFERARB, [HDC, c_int, c_int, c_int, POINTER(c_int)], 'ARB_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:408
wglGetPbufferDCARB = _link_function('wglGetPbufferDCARB', HDC, [HPBUFFERARB], 'ARB_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:409
wglReleasePbufferDCARB = _link_function('wglReleasePbufferDCARB', c_int, [HPBUFFERARB, HDC], 'ARB_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:410
wglDestroyPbufferARB = _link_function('wglDestroyPbufferARB', BOOL, [HPBUFFERARB], 'ARB_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:411
wglQueryPbufferARB = _link_function('wglQueryPbufferARB', BOOL, [HPBUFFERARB, c_int, POINTER(c_int)], 'ARB_pbuffer')

PFNWGLCREATEPBUFFERARBPROC = CFUNCTYPE(HPBUFFERARB, HDC, c_int, c_int, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:413
PFNWGLGETPBUFFERDCARBPROC = CFUNCTYPE(HDC, HPBUFFERARB) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:414
PFNWGLRELEASEPBUFFERDCARBPROC = CFUNCTYPE(c_int, HPBUFFERARB, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:415
PFNWGLDESTROYPBUFFERARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:416
PFNWGLQUERYPBUFFERARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:417
# ARB_render_texture (http://developer.download.nvidia.com/opengl/includes/wglext.h:420)
WGL_ARB_render_texture = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:421
# http://developer.download.nvidia.com/opengl/includes/wglext.h:423
wglBindTexImageARB = _link_function('wglBindTexImageARB', BOOL, [HPBUFFERARB, c_int], 'ARB_render_texture')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:424
wglReleaseTexImageARB = _link_function('wglReleaseTexImageARB', BOOL, [HPBUFFERARB, c_int], 'ARB_render_texture')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:425
wglSetPbufferAttribARB = _link_function('wglSetPbufferAttribARB', BOOL, [HPBUFFERARB, POINTER(c_int)], 'ARB_render_texture')

PFNWGLBINDTEXIMAGEARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:427
PFNWGLRELEASETEXIMAGEARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:428
PFNWGLSETPBUFFERATTRIBARBPROC = CFUNCTYPE(BOOL, HPBUFFERARB, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:429
# ARB_pixel_format_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:432)
WGL_ARB_pixel_format_float = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:433
# EXT_display_color_table (http://developer.download.nvidia.com/opengl/includes/wglext.h:436)
WGL_EXT_display_color_table = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:437
GLboolean = c_ubyte 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:18
GLushort = c_ushort 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:25
# http://developer.download.nvidia.com/opengl/includes/wglext.h:439
wglCreateDisplayColorTableEXT = _link_function('wglCreateDisplayColorTableEXT', GLboolean, [GLushort], 'EXT_display_color_table')

GLuint = c_uint 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:26
# http://developer.download.nvidia.com/opengl/includes/wglext.h:440
wglLoadDisplayColorTableEXT = _link_function('wglLoadDisplayColorTableEXT', GLboolean, [POINTER(GLushort), GLuint], 'EXT_display_color_table')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:441
wglBindDisplayColorTableEXT = _link_function('wglBindDisplayColorTableEXT', GLboolean, [GLushort], 'EXT_display_color_table')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:442
wglDestroyDisplayColorTableEXT = _link_function('wglDestroyDisplayColorTableEXT', VOID, [GLushort], 'EXT_display_color_table')

PFNWGLCREATEDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, GLushort) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:444
PFNWGLLOADDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, POINTER(GLushort), GLuint) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:445
PFNWGLBINDDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(GLboolean, GLushort) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:446
PFNWGLDESTROYDISPLAYCOLORTABLEEXTPROC = CFUNCTYPE(VOID, GLushort) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:447
# EXT_extensions_string (http://developer.download.nvidia.com/opengl/includes/wglext.h:450)
WGL_EXT_extensions_string = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:451
# http://developer.download.nvidia.com/opengl/includes/wglext.h:453
wglGetExtensionsStringEXT = _link_function('wglGetExtensionsStringEXT', c_char_p, [], 'EXT_extensions_string')

PFNWGLGETEXTENSIONSSTRINGEXTPROC = CFUNCTYPE(c_char_p) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:455
# EXT_make_current_read (http://developer.download.nvidia.com/opengl/includes/wglext.h:458)
WGL_EXT_make_current_read = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:459
# http://developer.download.nvidia.com/opengl/includes/wglext.h:461
wglMakeContextCurrentEXT = _link_function('wglMakeContextCurrentEXT', BOOL, [HDC, HDC, HGLRC], 'EXT_make_current_read')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:462
wglGetCurrentReadDCEXT = _link_function('wglGetCurrentReadDCEXT', HDC, [], 'EXT_make_current_read')

PFNWGLMAKECONTEXTCURRENTEXTPROC = CFUNCTYPE(BOOL, HDC, HDC, HGLRC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:464
PFNWGLGETCURRENTREADDCEXTPROC = CFUNCTYPE(HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:465
# EXT_pbuffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:468)
WGL_EXT_pbuffer = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:469
# http://developer.download.nvidia.com/opengl/includes/wglext.h:471
wglCreatePbufferEXT = _link_function('wglCreatePbufferEXT', HPBUFFEREXT, [HDC, c_int, c_int, c_int, POINTER(c_int)], 'EXT_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:472
wglGetPbufferDCEXT = _link_function('wglGetPbufferDCEXT', HDC, [HPBUFFEREXT], 'EXT_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:473
wglReleasePbufferDCEXT = _link_function('wglReleasePbufferDCEXT', c_int, [HPBUFFEREXT, HDC], 'EXT_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:474
wglDestroyPbufferEXT = _link_function('wglDestroyPbufferEXT', BOOL, [HPBUFFEREXT], 'EXT_pbuffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:475
wglQueryPbufferEXT = _link_function('wglQueryPbufferEXT', BOOL, [HPBUFFEREXT, c_int, POINTER(c_int)], 'EXT_pbuffer')

PFNWGLCREATEPBUFFEREXTPROC = CFUNCTYPE(HPBUFFEREXT, HDC, c_int, c_int, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:477
PFNWGLGETPBUFFERDCEXTPROC = CFUNCTYPE(HDC, HPBUFFEREXT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:478
PFNWGLRELEASEPBUFFERDCEXTPROC = CFUNCTYPE(c_int, HPBUFFEREXT, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:479
PFNWGLDESTROYPBUFFEREXTPROC = CFUNCTYPE(BOOL, HPBUFFEREXT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:480
PFNWGLQUERYPBUFFEREXTPROC = CFUNCTYPE(BOOL, HPBUFFEREXT, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:481
# EXT_pixel_format (http://developer.download.nvidia.com/opengl/includes/wglext.h:484)
WGL_EXT_pixel_format = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:485
# http://developer.download.nvidia.com/opengl/includes/wglext.h:487
wglGetPixelFormatAttribivEXT = _link_function('wglGetPixelFormatAttribivEXT', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)], 'EXT_pixel_format')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:488
wglGetPixelFormatAttribfvEXT = _link_function('wglGetPixelFormatAttribfvEXT', BOOL, [HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)], 'EXT_pixel_format')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:489
wglChoosePixelFormatEXT = _link_function('wglChoosePixelFormatEXT', BOOL, [HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)], 'EXT_pixel_format')

PFNWGLGETPIXELFORMATATTRIBIVEXTPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:491
PFNWGLGETPIXELFORMATATTRIBFVEXTPROC = CFUNCTYPE(BOOL, HDC, c_int, c_int, UINT, POINTER(c_int), POINTER(FLOAT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:492
PFNWGLCHOOSEPIXELFORMATEXTPROC = CFUNCTYPE(BOOL, HDC, POINTER(c_int), POINTER(FLOAT), UINT, POINTER(c_int), POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:493
# EXT_swap_control (http://developer.download.nvidia.com/opengl/includes/wglext.h:496)
WGL_EXT_swap_control = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:497
# http://developer.download.nvidia.com/opengl/includes/wglext.h:499
wglSwapIntervalEXT = _link_function('wglSwapIntervalEXT', BOOL, [c_int], 'EXT_swap_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:500
wglGetSwapIntervalEXT = _link_function('wglGetSwapIntervalEXT', c_int, [], 'EXT_swap_control')

PFNWGLSWAPINTERVALEXTPROC = CFUNCTYPE(BOOL, c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:502
PFNWGLGETSWAPINTERVALEXTPROC = CFUNCTYPE(c_int) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:503
# EXT_depth_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:506)
WGL_EXT_depth_float = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:507
# NV_vertex_array_range (http://developer.download.nvidia.com/opengl/includes/wglext.h:510)
WGL_NV_vertex_array_range = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:511
GLsizei = c_int 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:23
GLfloat = c_float 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:27
# http://developer.download.nvidia.com/opengl/includes/wglext.h:513
wglAllocateMemoryNV = _link_function('wglAllocateMemoryNV', POINTER(c_void), [GLsizei, GLfloat, GLfloat, GLfloat], 'NV_vertex_array_range')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:514
wglFreeMemoryNV = _link_function('wglFreeMemoryNV', None, [POINTER(None)], 'NV_vertex_array_range')

PFNWGLALLOCATEMEMORYNVPROC = CFUNCTYPE(POINTER(c_void), GLsizei, GLfloat, GLfloat, GLfloat) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:516
PFNWGLFREEMEMORYNVPROC = CFUNCTYPE(None, POINTER(None)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:517
# 3DFX_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:520)
WGL_3DFX_multisample = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:521
# EXT_multisample (http://developer.download.nvidia.com/opengl/includes/wglext.h:524)
WGL_EXT_multisample = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:525
# OML_sync_control (http://developer.download.nvidia.com/opengl/includes/wglext.h:528)
WGL_OML_sync_control = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:529

# http://developer.download.nvidia.com/opengl/includes/wglext.h:531
wglGetSyncValuesOML = _link_function('wglGetSyncValuesOML', BOOL, [HDC, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

INT32 = c_int 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:35
# http://developer.download.nvidia.com/opengl/includes/wglext.h:532
wglGetMscRateOML = _link_function('wglGetMscRateOML', BOOL, [HDC, POINTER(INT32), POINTER(INT32)], 'OML_sync_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:533
wglSwapBuffersMscOML = _link_function('wglSwapBuffersMscOML', INT64, [HDC, INT64, INT64, INT64], 'OML_sync_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:534
wglSwapLayerBuffersMscOML = _link_function('wglSwapLayerBuffersMscOML', INT64, [HDC, c_int, INT64, INT64, INT64], 'OML_sync_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:535
wglWaitForMscOML = _link_function('wglWaitForMscOML', BOOL, [HDC, INT64, INT64, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:536
wglWaitForSbcOML = _link_function('wglWaitForSbcOML', BOOL, [HDC, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)], 'OML_sync_control')

PFNWGLGETSYNCVALUESOMLPROC = CFUNCTYPE(BOOL, HDC, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:538
PFNWGLGETMSCRATEOMLPROC = CFUNCTYPE(BOOL, HDC, POINTER(INT32), POINTER(INT32)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:539
PFNWGLSWAPBUFFERSMSCOMLPROC = CFUNCTYPE(INT64, HDC, INT64, INT64, INT64) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:540
PFNWGLSWAPLAYERBUFFERSMSCOMLPROC = CFUNCTYPE(INT64, HDC, c_int, INT64, INT64, INT64) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:541
PFNWGLWAITFORMSCOMLPROC = CFUNCTYPE(BOOL, HDC, INT64, INT64, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:542
PFNWGLWAITFORSBCOMLPROC = CFUNCTYPE(BOOL, HDC, INT64, POINTER(INT64), POINTER(INT64), POINTER(INT64)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:543
# I3D_digital_video_control (http://developer.download.nvidia.com/opengl/includes/wglext.h:546)
WGL_I3D_digital_video_control = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:547
# http://developer.download.nvidia.com/opengl/includes/wglext.h:549
wglGetDigitalVideoParametersI3D = _link_function('wglGetDigitalVideoParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_digital_video_control')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:550
wglSetDigitalVideoParametersI3D = _link_function('wglSetDigitalVideoParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_digital_video_control')

PFNWGLGETDIGITALVIDEOPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:552
PFNWGLSETDIGITALVIDEOPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:553
# I3D_gamma (http://developer.download.nvidia.com/opengl/includes/wglext.h:556)
WGL_I3D_gamma = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:557
# http://developer.download.nvidia.com/opengl/includes/wglext.h:559
wglGetGammaTableParametersI3D = _link_function('wglGetGammaTableParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_gamma')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:560
wglSetGammaTableParametersI3D = _link_function('wglSetGammaTableParametersI3D', BOOL, [HDC, c_int, POINTER(c_int)], 'I3D_gamma')

USHORT = c_ushort 	# C:\cygwin\home\alex\projects\pyglet\tools\wgl.h:49
# http://developer.download.nvidia.com/opengl/includes/wglext.h:561
wglGetGammaTableI3D = _link_function('wglGetGammaTableI3D', BOOL, [HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)], 'I3D_gamma')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:562
wglSetGammaTableI3D = _link_function('wglSetGammaTableI3D', BOOL, [HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)], 'I3D_gamma')

PFNWGLGETGAMMATABLEPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:564
PFNWGLSETGAMMATABLEPARAMETERSI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(c_int)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:565
PFNWGLGETGAMMATABLEI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:566
PFNWGLSETGAMMATABLEI3DPROC = CFUNCTYPE(BOOL, HDC, c_int, POINTER(USHORT), POINTER(USHORT), POINTER(USHORT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:567
# I3D_genlock (http://developer.download.nvidia.com/opengl/includes/wglext.h:570)
WGL_I3D_genlock = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:571
# http://developer.download.nvidia.com/opengl/includes/wglext.h:573
wglEnableGenlockI3D = _link_function('wglEnableGenlockI3D', BOOL, [HDC], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:574
wglDisableGenlockI3D = _link_function('wglDisableGenlockI3D', BOOL, [HDC], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:575
wglIsEnabledGenlockI3D = _link_function('wglIsEnabledGenlockI3D', BOOL, [HDC, POINTER(BOOL)], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:576
wglGenlockSourceI3D = _link_function('wglGenlockSourceI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:577
wglGetGenlockSourceI3D = _link_function('wglGetGenlockSourceI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:578
wglGenlockSourceEdgeI3D = _link_function('wglGenlockSourceEdgeI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:579
wglGetGenlockSourceEdgeI3D = _link_function('wglGetGenlockSourceEdgeI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:580
wglGenlockSampleRateI3D = _link_function('wglGenlockSampleRateI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:581
wglGetGenlockSampleRateI3D = _link_function('wglGetGenlockSampleRateI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:582
wglGenlockSourceDelayI3D = _link_function('wglGenlockSourceDelayI3D', BOOL, [HDC, UINT], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:583
wglGetGenlockSourceDelayI3D = _link_function('wglGetGenlockSourceDelayI3D', BOOL, [HDC, POINTER(UINT)], 'I3D_genlock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:584
wglQueryGenlockMaxSourceDelayI3D = _link_function('wglQueryGenlockMaxSourceDelayI3D', BOOL, [HDC, POINTER(UINT), POINTER(UINT)], 'I3D_genlock')

PFNWGLENABLEGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:586
PFNWGLDISABLEGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:587
PFNWGLISENABLEDGENLOCKI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(BOOL)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:588
PFNWGLGENLOCKSOURCEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:589
PFNWGLGETGENLOCKSOURCEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:590
PFNWGLGENLOCKSOURCEEDGEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:591
PFNWGLGETGENLOCKSOURCEEDGEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:592
PFNWGLGENLOCKSAMPLERATEI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:593
PFNWGLGETGENLOCKSAMPLERATEI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:594
PFNWGLGENLOCKSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:595
PFNWGLGETGENLOCKSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:596
PFNWGLQUERYGENLOCKMAXSOURCEDELAYI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(UINT), POINTER(UINT)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:597
# I3D_image_buffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:600)
WGL_I3D_image_buffer = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:601

# http://developer.download.nvidia.com/opengl/includes/wglext.h:603
wglCreateImageBufferI3D = _link_function('wglCreateImageBufferI3D', LPVOID, [HDC, DWORD, UINT], 'I3D_image_buffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:604
wglDestroyImageBufferI3D = _link_function('wglDestroyImageBufferI3D', BOOL, [HDC, LPVOID], 'I3D_image_buffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:605
wglAssociateImageBufferEventsI3D = _link_function('wglAssociateImageBufferEventsI3D', BOOL, [HDC, POINTER(HANDLE), POINTER(LPVOID), POINTER(DWORD), UINT], 'I3D_image_buffer')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:606
wglReleaseImageBufferEventsI3D = _link_function('wglReleaseImageBufferEventsI3D', BOOL, [HDC, POINTER(LPVOID), UINT], 'I3D_image_buffer')

PFNWGLCREATEIMAGEBUFFERI3DPROC = CFUNCTYPE(LPVOID, HDC, DWORD, UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:608
PFNWGLDESTROYIMAGEBUFFERI3DPROC = CFUNCTYPE(BOOL, HDC, LPVOID) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:609
PFNWGLASSOCIATEIMAGEBUFFEREVENTSI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(HANDLE), POINTER(LPVOID), POINTER(DWORD), UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:610
PFNWGLRELEASEIMAGEBUFFEREVENTSI3DPROC = CFUNCTYPE(BOOL, HDC, POINTER(LPVOID), UINT) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:611
# I3D_swap_frame_lock (http://developer.download.nvidia.com/opengl/includes/wglext.h:614)
WGL_I3D_swap_frame_lock = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:615
# http://developer.download.nvidia.com/opengl/includes/wglext.h:617
wglEnableFrameLockI3D = _link_function('wglEnableFrameLockI3D', BOOL, [], 'I3D_swap_frame_lock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:618
wglDisableFrameLockI3D = _link_function('wglDisableFrameLockI3D', BOOL, [], 'I3D_swap_frame_lock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:619
wglIsEnabledFrameLockI3D = _link_function('wglIsEnabledFrameLockI3D', BOOL, [POINTER(BOOL)], 'I3D_swap_frame_lock')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:620
wglQueryFrameLockMasterI3D = _link_function('wglQueryFrameLockMasterI3D', BOOL, [POINTER(BOOL)], 'I3D_swap_frame_lock')

PFNWGLENABLEFRAMELOCKI3DPROC = CFUNCTYPE(BOOL) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:622
PFNWGLDISABLEFRAMELOCKI3DPROC = CFUNCTYPE(BOOL) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:623
PFNWGLISENABLEDFRAMELOCKI3DPROC = CFUNCTYPE(BOOL, POINTER(BOOL)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:624
PFNWGLQUERYFRAMELOCKMASTERI3DPROC = CFUNCTYPE(BOOL, POINTER(BOOL)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:625
# I3D_swap_frame_usage (http://developer.download.nvidia.com/opengl/includes/wglext.h:628)
WGL_I3D_swap_frame_usage = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:629
# http://developer.download.nvidia.com/opengl/includes/wglext.h:631
wglGetFrameUsageI3D = _link_function('wglGetFrameUsageI3D', BOOL, [POINTER(c_float)], 'I3D_swap_frame_usage')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:632
wglBeginFrameTrackingI3D = _link_function('wglBeginFrameTrackingI3D', BOOL, [], 'I3D_swap_frame_usage')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:633
wglEndFrameTrackingI3D = _link_function('wglEndFrameTrackingI3D', BOOL, [], 'I3D_swap_frame_usage')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:634
wglQueryFrameTrackingI3D = _link_function('wglQueryFrameTrackingI3D', BOOL, [POINTER(DWORD), POINTER(DWORD), POINTER(c_float)], 'I3D_swap_frame_usage')

PFNWGLGETFRAMEUSAGEI3DPROC = CFUNCTYPE(BOOL, POINTER(c_float)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:636
PFNWGLBEGINFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:637
PFNWGLENDFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:638
PFNWGLQUERYFRAMETRACKINGI3DPROC = CFUNCTYPE(BOOL, POINTER(DWORD), POINTER(DWORD), POINTER(c_float)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:639
# ATI_pixel_format_float (http://developer.download.nvidia.com/opengl/includes/wglext.h:642)
WGL_ATI_pixel_format_float = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:643
# NV_render_depth_texture (http://developer.download.nvidia.com/opengl/includes/wglext.h:646)
WGL_NV_render_depth_texture = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:647
# NV_render_texture_rectangle (http://developer.download.nvidia.com/opengl/includes/wglext.h:650)
WGL_NV_render_texture_rectangle = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:651
# NV_float_buffer (http://developer.download.nvidia.com/opengl/includes/wglext.h:654)
WGL_NV_float_buffer = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:655
# NV_swap_group (http://developer.download.nvidia.com/opengl/includes/wglext.h:658)
WGL_NV_swap_group = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:659
# http://developer.download.nvidia.com/opengl/includes/wglext.h:661
wglJoinSwapGroupNV = _link_function('wglJoinSwapGroupNV', BOOL, [HDC, GLuint], 'NV_swap_group')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:662
wglBindSwapBarrierNV = _link_function('wglBindSwapBarrierNV', BOOL, [GLuint, GLuint], 'NV_swap_group')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:663
wglQuerySwapGroupNV = _link_function('wglQuerySwapGroupNV', BOOL, [HDC, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:664
wglQueryMaxSwapGroupsNV = _link_function('wglQueryMaxSwapGroupsNV', BOOL, [HDC, POINTER(GLuint), POINTER(GLuint)], 'NV_swap_group')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:665
wglQueryFrameCountNV = _link_function('wglQueryFrameCountNV', BOOL, [HDC, POINTER(GLuint)], 'NV_swap_group')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:666
wglResetFrameCountNV = _link_function('wglResetFrameCountNV', BOOL, [HDC], 'NV_swap_group')

PFNWGLJOINSWAPGROUPNVPROC = CFUNCTYPE(BOOL, HDC, GLuint) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:668
PFNWGLBINDSWAPBARRIERNVPROC = CFUNCTYPE(BOOL, GLuint, GLuint) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:669
PFNWGLQUERYSWAPGROUPNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint), POINTER(GLuint)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:670
PFNWGLQUERYMAXSWAPGROUPSNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint), POINTER(GLuint)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:671
PFNWGLQUERYFRAMECOUNTNVPROC = CFUNCTYPE(BOOL, HDC, POINTER(GLuint)) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:672
PFNWGLRESETFRAMECOUNTNVPROC = CFUNCTYPE(BOOL, HDC) 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:673
# NV_gpu_affinity (http://developer.download.nvidia.com/opengl/includes/wglext.h:676)
WGL_NV_gpu_affinity = 1 	# http://developer.download.nvidia.com/opengl/includes/wglext.h:677
# http://developer.download.nvidia.com/opengl/includes/wglext.h:679
wglEnumGpusNV = _link_function('wglEnumGpusNV', BOOL, [UINT, POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:680
wglEnumGpuDevicesNV = _link_function('wglEnumGpuDevicesNV', BOOL, [HGPUNV, UINT, PGPU_DEVICE], 'NV_gpu_affinity')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:681
wglCreateAffinityDCNV = _link_function('wglCreateAffinityDCNV', HDC, [POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:682
wglEnumGpusFromAffinityDCNV = _link_function('wglEnumGpusFromAffinityDCNV', BOOL, [HDC, UINT, POINTER(HGPUNV)], 'NV_gpu_affinity')

# http://developer.download.nvidia.com/opengl/includes/wglext.h:683
wglDeleteDCNV = _link_function('wglDeleteDCNV', BOOL, [HDC], 'NV_gpu_affinity')


__all__ = [
    'ERROR_INCOMPATIBLE_DEVICE_CONTEXTS_ARB',
    'ERROR_INVALID_PIXEL_TYPE_ARB',
    'ERROR_INVALID_PIXEL_TYPE_EXT',
    'GPU_DEVICE',
    'HGPUNV',
    'HPBUFFERARB',
    'HPBUFFEREXT',
    'PFNWGLALLOCATEMEMORYNVPROC',
    'PFNWGLASSOCIATEIMAGEBUFFEREVENTSI3DPROC',
    'PFNWGLBEGINFRAMETRACKINGI3DPROC',
    'PFNWGLBINDDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLBINDSWAPBARRIERNVPROC',
    'PFNWGLBINDTEXIMAGEARBPROC',
    'PFNWGLCHOOSEPIXELFORMATARBPROC',
    'PFNWGLCHOOSEPIXELFORMATEXTPROC',
    'PFNWGLCREATEBUFFERREGIONARBPROC',
    'PFNWGLCREATEDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLCREATEIMAGEBUFFERI3DPROC',
    'PFNWGLCREATEPBUFFERARBPROC',
    'PFNWGLCREATEPBUFFEREXTPROC',
    'PFNWGLDELETEBUFFERREGIONARBPROC',
    'PFNWGLDESTROYDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLDESTROYIMAGEBUFFERI3DPROC',
    'PFNWGLDESTROYPBUFFERARBPROC',
    'PFNWGLDESTROYPBUFFEREXTPROC',
    'PFNWGLDISABLEFRAMELOCKI3DPROC',
    'PFNWGLDISABLEGENLOCKI3DPROC',
    'PFNWGLENABLEFRAMELOCKI3DPROC',
    'PFNWGLENABLEGENLOCKI3DPROC',
    'PFNWGLENDFRAMETRACKINGI3DPROC',
    'PFNWGLFREEMEMORYNVPROC',
    'PFNWGLGENLOCKSAMPLERATEI3DPROC',
    'PFNWGLGENLOCKSOURCEDELAYI3DPROC',
    'PFNWGLGENLOCKSOURCEEDGEI3DPROC',
    'PFNWGLGENLOCKSOURCEI3DPROC',
    'PFNWGLGETCURRENTREADDCARBPROC',
    'PFNWGLGETCURRENTREADDCEXTPROC',
    'PFNWGLGETDIGITALVIDEOPARAMETERSI3DPROC',
    'PFNWGLGETEXTENSIONSSTRINGARBPROC',
    'PFNWGLGETEXTENSIONSSTRINGEXTPROC',
    'PFNWGLGETFRAMEUSAGEI3DPROC',
    'PFNWGLGETGAMMATABLEI3DPROC',
    'PFNWGLGETGAMMATABLEPARAMETERSI3DPROC',
    'PFNWGLGETGENLOCKSAMPLERATEI3DPROC',
    'PFNWGLGETGENLOCKSOURCEDELAYI3DPROC',
    'PFNWGLGETGENLOCKSOURCEEDGEI3DPROC',
    'PFNWGLGETGENLOCKSOURCEI3DPROC',
    'PFNWGLGETMSCRATEOMLPROC',
    'PFNWGLGETPBUFFERDCARBPROC',
    'PFNWGLGETPBUFFERDCEXTPROC',
    'PFNWGLGETPIXELFORMATATTRIBFVARBPROC',
    'PFNWGLGETPIXELFORMATATTRIBFVEXTPROC',
    'PFNWGLGETPIXELFORMATATTRIBIVARBPROC',
    'PFNWGLGETPIXELFORMATATTRIBIVEXTPROC',
    'PFNWGLGETSWAPINTERVALEXTPROC',
    'PFNWGLGETSYNCVALUESOMLPROC',
    'PFNWGLISENABLEDFRAMELOCKI3DPROC',
    'PFNWGLISENABLEDGENLOCKI3DPROC',
    'PFNWGLJOINSWAPGROUPNVPROC',
    'PFNWGLLOADDISPLAYCOLORTABLEEXTPROC',
    'PFNWGLMAKECONTEXTCURRENTARBPROC',
    'PFNWGLMAKECONTEXTCURRENTEXTPROC',
    'PFNWGLQUERYFRAMECOUNTNVPROC',
    'PFNWGLQUERYFRAMELOCKMASTERI3DPROC',
    'PFNWGLQUERYFRAMETRACKINGI3DPROC',
    'PFNWGLQUERYGENLOCKMAXSOURCEDELAYI3DPROC',
    'PFNWGLQUERYMAXSWAPGROUPSNVPROC',
    'PFNWGLQUERYPBUFFERARBPROC',
    'PFNWGLQUERYPBUFFEREXTPROC',
    'PFNWGLQUERYSWAPGROUPNVPROC',
    'PFNWGLRELEASEIMAGEBUFFEREVENTSI3DPROC',
    'PFNWGLRELEASEPBUFFERDCARBPROC',
    'PFNWGLRELEASEPBUFFERDCEXTPROC',
    'PFNWGLRELEASETEXIMAGEARBPROC',
    'PFNWGLRESETFRAMECOUNTNVPROC',
    'PFNWGLRESTOREBUFFERREGIONARBPROC',
    'PFNWGLSAVEBUFFERREGIONARBPROC',
    'PFNWGLSETDIGITALVIDEOPARAMETERSI3DPROC',
    'PFNWGLSETGAMMATABLEI3DPROC',
    'PFNWGLSETGAMMATABLEPARAMETERSI3DPROC',
    'PFNWGLSETPBUFFERATTRIBARBPROC',
    'PFNWGLSWAPBUFFERSMSCOMLPROC',
    'PFNWGLSWAPINTERVALEXTPROC',
    'PFNWGLSWAPLAYERBUFFERSMSCOMLPROC',
    'PFNWGLWAITFORMSCOMLPROC',
    'PFNWGLWAITFORSBCOMLPROC',
    'PGPU_DEVICE',
    'WGL_ACCELERATION_ARB',
    'WGL_ACCELERATION_EXT',
    'WGL_ACCUM_ALPHA_BITS_ARB',
    'WGL_ACCUM_ALPHA_BITS_EXT',
    'WGL_ACCUM_BITS_ARB',
    'WGL_ACCUM_BITS_EXT',
    'WGL_ACCUM_BLUE_BITS_ARB',
    'WGL_ACCUM_BLUE_BITS_EXT',
    'WGL_ACCUM_GREEN_BITS_ARB',
    'WGL_ACCUM_GREEN_BITS_EXT',
    'WGL_ACCUM_RED_BITS_ARB',
    'WGL_ACCUM_RED_BITS_EXT',
    'WGL_ALPHA_BITS_ARB',
    'WGL_ALPHA_BITS_EXT',
    'WGL_ALPHA_SHIFT_ARB',
    'WGL_ALPHA_SHIFT_EXT',
    'WGL_AUX0_ARB',
    'WGL_AUX1_ARB',
    'WGL_AUX2_ARB',
    'WGL_AUX3_ARB',
    'WGL_AUX4_ARB',
    'WGL_AUX5_ARB',
    'WGL_AUX6_ARB',
    'WGL_AUX7_ARB',
    'WGL_AUX8_ARB',
    'WGL_AUX9_ARB',
    'WGL_AUX_BUFFERS_ARB',
    'WGL_AUX_BUFFERS_EXT',
    'WGL_BACK_COLOR_BUFFER_BIT_ARB',
    'WGL_BACK_LEFT_ARB',
    'WGL_BACK_RIGHT_ARB',
    'WGL_BIND_TO_TEXTURE_DEPTH_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGBA_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGB_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RG_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_R_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_RGBA_NV',
    'WGL_BIND_TO_TEXTURE_RECTANGLE_RGB_NV',
    'WGL_BIND_TO_TEXTURE_RGBA_ARB',
    'WGL_BIND_TO_TEXTURE_RGB_ARB',
    'WGL_BLUE_BITS_ARB',
    'WGL_BLUE_BITS_EXT',
    'WGL_BLUE_SHIFT_ARB',
    'WGL_BLUE_SHIFT_EXT',
    'WGL_COLOR_BITS_ARB',
    'WGL_COLOR_BITS_EXT',
    'WGL_COLOR_CLEAR_UNCLAMPED_VALUE_ATI',
    'WGL_CUBE_MAP_FACE_ARB',
    'WGL_DEPTH_BITS_ARB',
    'WGL_DEPTH_BITS_EXT',
    'WGL_DEPTH_BUFFER_BIT_ARB',
    'WGL_DEPTH_COMPONENT_NV',
    'WGL_DEPTH_FLOAT_EXT',
    'WGL_DEPTH_TEXTURE_FORMAT_NV',
    'WGL_DIGITAL_VIDEO_CURSOR_ALPHA_FRAMEBUFFER_I3D',
    'WGL_DIGITAL_VIDEO_CURSOR_ALPHA_VALUE_I3D',
    'WGL_DIGITAL_VIDEO_CURSOR_INCLUDED_I3D',
    'WGL_DIGITAL_VIDEO_GAMMA_CORRECTED_I3D',
    'WGL_DOUBLE_BUFFER_ARB',
    'WGL_DOUBLE_BUFFER_EXT',
    'WGL_DRAW_TO_BITMAP_ARB',
    'WGL_DRAW_TO_BITMAP_EXT',
    'WGL_DRAW_TO_PBUFFER_ARB',
    'WGL_DRAW_TO_PBUFFER_EXT',
    'WGL_DRAW_TO_WINDOW_ARB',
    'WGL_DRAW_TO_WINDOW_EXT',
    'WGL_ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV',
    'WGL_ERROR_MISSING_AFFINITY_MASK_NV',
    'WGL_FLOAT_COMPONENTS_NV',
    'WGL_FRONT_COLOR_BUFFER_BIT_ARB',
    'WGL_FRONT_LEFT_ARB',
    'WGL_FRONT_RIGHT_ARB',
    'WGL_FULL_ACCELERATION_ARB',
    'WGL_FULL_ACCELERATION_EXT',
    'WGL_GAMMA_EXCLUDE_DESKTOP_I3D',
    'WGL_GAMMA_TABLE_SIZE_I3D',
    'WGL_GENERIC_ACCELERATION_ARB',
    'WGL_GENERIC_ACCELERATION_EXT',
    'WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D',
    'WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D',
    'WGL_GENLOCK_SOURCE_EDGE_RISING_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_FIELD_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_SYNC_I3D',
    'WGL_GENLOCK_SOURCE_EXTENAL_TTL_I3D',
    'WGL_GENLOCK_SOURCE_MULTIVIEW_I3D',
    'WGL_GREEN_BITS_ARB',
    'WGL_GREEN_BITS_EXT',
    'WGL_GREEN_SHIFT_ARB',
    'WGL_GREEN_SHIFT_EXT',
    'WGL_IMAGE_BUFFER_LOCK_I3D',
    'WGL_IMAGE_BUFFER_MIN_ACCESS_I3D',
    'WGL_MAX_PBUFFER_HEIGHT_ARB',
    'WGL_MAX_PBUFFER_HEIGHT_EXT',
    'WGL_MAX_PBUFFER_PIXELS_ARB',
    'WGL_MAX_PBUFFER_PIXELS_EXT',
    'WGL_MAX_PBUFFER_WIDTH_ARB',
    'WGL_MAX_PBUFFER_WIDTH_EXT',
    'WGL_MIPMAP_LEVEL_ARB',
    'WGL_MIPMAP_TEXTURE_ARB',
    'WGL_NEED_PALETTE_ARB',
    'WGL_NEED_PALETTE_EXT',
    'WGL_NEED_SYSTEM_PALETTE_ARB',
    'WGL_NEED_SYSTEM_PALETTE_EXT',
    'WGL_NO_ACCELERATION_ARB',
    'WGL_NO_ACCELERATION_EXT',
    'WGL_NO_TEXTURE_ARB',
    'WGL_NUMBER_OVERLAYS_ARB',
    'WGL_NUMBER_OVERLAYS_EXT',
    'WGL_NUMBER_PIXEL_FORMATS_ARB',
    'WGL_NUMBER_PIXEL_FORMATS_EXT',
    'WGL_NUMBER_UNDERLAYS_ARB',
    'WGL_NUMBER_UNDERLAYS_EXT',
    'WGL_OPTIMAL_PBUFFER_HEIGHT_EXT',
    'WGL_OPTIMAL_PBUFFER_WIDTH_EXT',
    'WGL_PBUFFER_HEIGHT_ARB',
    'WGL_PBUFFER_HEIGHT_EXT',
    'WGL_PBUFFER_LARGEST_ARB',
    'WGL_PBUFFER_LARGEST_EXT',
    'WGL_PBUFFER_LOST_ARB',
    'WGL_PBUFFER_WIDTH_ARB',
    'WGL_PBUFFER_WIDTH_EXT',
    'WGL_PIXEL_TYPE_ARB',
    'WGL_PIXEL_TYPE_EXT',
    'WGL_RED_BITS_ARB',
    'WGL_RED_BITS_EXT',
    'WGL_RED_SHIFT_ARB',
    'WGL_RED_SHIFT_EXT',
    'WGL_RGBA_FLOAT_MODE_ATI',
    'WGL_SAMPLES_3DFX',
    'WGL_SAMPLES_ARB',
    'WGL_SAMPLES_EXT',
    'WGL_SAMPLE_BUFFERS_3DFX',
    'WGL_SAMPLE_BUFFERS_ARB',
    'WGL_SAMPLE_BUFFERS_EXT',
    'WGL_SHARE_ACCUM_ARB',
    'WGL_SHARE_ACCUM_EXT',
    'WGL_SHARE_DEPTH_ARB',
    'WGL_SHARE_DEPTH_EXT',
    'WGL_SHARE_STENCIL_ARB',
    'WGL_SHARE_STENCIL_EXT',
    'WGL_STENCIL_BITS_ARB',
    'WGL_STENCIL_BITS_EXT',
    'WGL_STENCIL_BUFFER_BIT_ARB',
    'WGL_STEREO_ARB',
    'WGL_STEREO_EXT',
    'WGL_SUPPORT_GDI_ARB',
    'WGL_SUPPORT_GDI_EXT',
    'WGL_SUPPORT_OPENGL_ARB',
    'WGL_SUPPORT_OPENGL_EXT',
    'WGL_SWAP_COPY_ARB',
    'WGL_SWAP_COPY_EXT',
    'WGL_SWAP_EXCHANGE_ARB',
    'WGL_SWAP_EXCHANGE_EXT',
    'WGL_SWAP_LAYER_BUFFERS_ARB',
    'WGL_SWAP_LAYER_BUFFERS_EXT',
    'WGL_SWAP_METHOD_ARB',
    'WGL_SWAP_METHOD_EXT',
    'WGL_SWAP_UNDEFINED_ARB',
    'WGL_SWAP_UNDEFINED_EXT',
    'WGL_TEXTURE_1D_ARB',
    'WGL_TEXTURE_2D_ARB',
    'WGL_TEXTURE_CUBE_MAP_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB',
    'WGL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB',
    'WGL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB',
    'WGL_TEXTURE_DEPTH_COMPONENT_NV',
    'WGL_TEXTURE_FLOAT_RGBA_NV',
    'WGL_TEXTURE_FLOAT_RGB_NV',
    'WGL_TEXTURE_FLOAT_RG_NV',
    'WGL_TEXTURE_FLOAT_R_NV',
    'WGL_TEXTURE_FORMAT_ARB',
    'WGL_TEXTURE_RECTANGLE_NV',
    'WGL_TEXTURE_RGBA_ARB',
    'WGL_TEXTURE_RGB_ARB',
    'WGL_TEXTURE_TARGET_ARB',
    'WGL_TRANSPARENT_ALPHA_VALUE_ARB',
    'WGL_TRANSPARENT_ARB',
    'WGL_TRANSPARENT_BLUE_VALUE_ARB',
    'WGL_TRANSPARENT_EXT',
    'WGL_TRANSPARENT_GREEN_VALUE_ARB',
    'WGL_TRANSPARENT_INDEX_VALUE_ARB',
    'WGL_TRANSPARENT_RED_VALUE_ARB',
    'WGL_TRANSPARENT_VALUE_EXT',
    'WGL_TYPE_COLORINDEX_ARB',
    'WGL_TYPE_COLORINDEX_EXT',
    'WGL_TYPE_RGBA_ARB',
    'WGL_TYPE_RGBA_EXT',
    'WGL_TYPE_RGBA_FLOAT_ARB',
    'WGL_TYPE_RGBA_FLOAT_ATI',
    'WGL_WGLEXT_VERSION',
    'WIN32_LEAN_AND_MEAN',
    'WGL_3DFX_multisample',
    'WGL_ARB_buffer_region',
    'WGL_ARB_extensions_string',
    'WGL_ARB_make_current_read',
    'WGL_ARB_multisample',
    'WGL_ARB_pbuffer',
    'WGL_ARB_pixel_format',
    'WGL_ARB_pixel_format_float',
    'WGL_ARB_render_texture',
    'WGL_ATI_pixel_format_float',
    'WGL_EXT_depth_float',
    'WGL_EXT_display_color_table',
    'WGL_EXT_extensions_string',
    'WGL_EXT_make_current_read',
    'WGL_EXT_multisample',
    'WGL_EXT_pbuffer',
    'WGL_EXT_pixel_format',
    'WGL_EXT_swap_control',
    'WGL_I3D_digital_video_control',
    'WGL_I3D_gamma',
    'WGL_I3D_genlock',
    'WGL_I3D_image_buffer',
    'WGL_I3D_swap_frame_lock',
    'WGL_I3D_swap_frame_usage',
    'WGL_NV_float_buffer',
    'WGL_NV_gpu_affinity',
    'WGL_NV_render_depth_texture',
    'WGL_NV_render_texture_rectangle',
    'WGL_NV_swap_group',
    'WGL_NV_vertex_array_range',
    'WGL_OML_sync_control',
    'wglAllocateMemoryNV',
    'wglAssociateImageBufferEventsI3D',
    'wglBeginFrameTrackingI3D',
    'wglBindDisplayColorTableEXT',
    'wglBindSwapBarrierNV',
    'wglBindTexImageARB',
    'wglChoosePixelFormatARB',
    'wglChoosePixelFormatEXT',
    'wglCreateAffinityDCNV',
    'wglCreateBufferRegionARB',
    'wglCreateDisplayColorTableEXT',
    'wglCreateImageBufferI3D',
    'wglCreatePbufferARB',
    'wglCreatePbufferEXT',
    'wglDeleteBufferRegionARB',
    'wglDeleteDCNV',
    'wglDestroyDisplayColorTableEXT',
    'wglDestroyImageBufferI3D',
    'wglDestroyPbufferARB',
    'wglDestroyPbufferEXT',
    'wglDisableFrameLockI3D',
    'wglDisableGenlockI3D',
    'wglEnableFrameLockI3D',
    'wglEnableGenlockI3D',
    'wglEndFrameTrackingI3D',
    'wglEnumGpuDevicesNV',
    'wglEnumGpusFromAffinityDCNV',
    'wglEnumGpusNV',
    'wglFreeMemoryNV',
    'wglGenlockSampleRateI3D',
    'wglGenlockSourceDelayI3D',
    'wglGenlockSourceEdgeI3D',
    'wglGenlockSourceI3D',
    'wglGetCurrentReadDCARB',
    'wglGetCurrentReadDCEXT',
    'wglGetDigitalVideoParametersI3D',
    'wglGetExtensionsStringARB',
    'wglGetExtensionsStringEXT',
    'wglGetFrameUsageI3D',
    'wglGetGammaTableI3D',
    'wglGetGammaTableParametersI3D',
    'wglGetGenlockSampleRateI3D',
    'wglGetGenlockSourceDelayI3D',
    'wglGetGenlockSourceEdgeI3D',
    'wglGetGenlockSourceI3D',
    'wglGetMscRateOML',
    'wglGetPbufferDCARB',
    'wglGetPbufferDCEXT',
    'wglGetPixelFormatAttribfvARB',
    'wglGetPixelFormatAttribfvEXT',
    'wglGetPixelFormatAttribivARB',
    'wglGetPixelFormatAttribivEXT',
    'wglGetSwapIntervalEXT',
    'wglGetSyncValuesOML',
    'wglIsEnabledFrameLockI3D',
    'wglIsEnabledGenlockI3D',
    'wglJoinSwapGroupNV',
    'wglLoadDisplayColorTableEXT',
    'wglMakeContextCurrentARB',
    'wglMakeContextCurrentEXT',
    'wglQueryFrameCountNV',
    'wglQueryFrameLockMasterI3D',
    'wglQueryFrameTrackingI3D',
    'wglQueryGenlockMaxSourceDelayI3D',
    'wglQueryMaxSwapGroupsNV',
    'wglQueryPbufferARB',
    'wglQueryPbufferEXT',
    'wglQuerySwapGroupNV',
    'wglReleaseImageBufferEventsI3D',
    'wglReleasePbufferDCARB',
    'wglReleasePbufferDCEXT',
    'wglReleaseTexImageARB',
    'wglResetFrameCountNV',
    'wglRestoreBufferRegionARB',
    'wglSaveBufferRegionARB',
    'wglSetDigitalVideoParametersI3D',
    'wglSetGammaTableI3D',
    'wglSetGammaTableParametersI3D',
    'wglSetPbufferAttribARB',
    'wglSwapBuffersMscOML',
    'wglSwapIntervalEXT',
    'wglSwapLayerBuffersMscOML',
    'wglWaitForMscOML',
    'wglWaitForSbcOML',
]
# END GENERATED CONTENT (do not edit above this line)


