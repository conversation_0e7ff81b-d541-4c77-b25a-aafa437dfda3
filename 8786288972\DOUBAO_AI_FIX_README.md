# 豆包AI自动发送功能修复说明

## 问题描述
豆包AI的自动发送功能不工作，可能的原因：
1. 豆包AI网站更新了DOM结构
2. 现有的选择器不再匹配正确的元素
3. 自动发送的时序需要调整

## 修复内容

### 1. 更新了输入框选择器
```javascript
// 原来的选择器
"textarea, .input-area textarea, #chat-input, .chat-input, [contenteditable='true'], .ProseMirror"

// 新的选择器（增加了更多可能的类型）
"textarea, .input-area textarea, #chat-input, .chat-input, [contenteditable='true'], .ProseMirror, div[contenteditable='true'], .chat-input-area textarea, .message-input, .input-box textarea, [data-testid='chat-input'], .composer-input"
```

### 2. 更新了发送按钮选择器
```javascript
// 原来的选择器
'button[aria-label="发送"], button[title="发送"], .send-button, button[type="submit"], .submit-btn, [data-testid="send-button"], button[aria-label="Send Message"]'

// 新的选择器（增加了更多可能的类型）
'button[aria-label="发送"], button[title="发送"], .send-button, button[type="submit"], .submit-btn, [data-testid="send-button"], button[aria-label="Send Message"], .send-btn, .submit-button, button.send, [data-testid="submit-button"], button[title="Send"], .chat-send-button'
```

### 3. 增强了调试功能
- 添加了详细的DOM结构分析
- 增加了多种按钮查找策略
- 添加了通过文本内容查找按钮的备选方案
- 增加了Enter键发送的备选方案

### 4. 调整了时序
- 剪贴板粘贴延迟：500ms → 800ms（豆包AI）
- 发送按钮点击延迟：600ms → 1200ms（豆包AI）
- 剪贴板恢复延迟：700ms → 1500ms（豆包AI）

### 5. 添加了全局调试函数
在豆包AI页面的控制台中可以调用 `window.debugDoubaoAI()` 来分析DOM结构。

## 调试方法

### 方法1：使用测试脚本
1. 运行 `test_doubao_debug.py`
2. 复制生成的JavaScript代码
3. 在豆包AI页面的控制台中运行
4. 查看输出结果

### 方法2：使用调试书签
1. 将生成的书签代码保存为浏览器书签
2. 在豆包AI页面点击书签
3. 查看控制台输出

### 方法3：手动调试
1. 在豆包AI页面按F12打开开发者工具
2. 在控制台中运行：`window.debugDoubaoAI()`
3. 查看详细的DOM分析结果

## 验证步骤

1. **重启Anki**：确保修改生效
2. **打开豆包AI**：在插件中切换到豆包AI
3. **测试自动发送**：
   - 在Anki中选择一些文本
   - 使用右键菜单或快捷键发送到豆包AI
   - 观察是否自动填入并发送

4. **查看调试信息**：
   - 打开浏览器开发者工具
   - 查看控制台输出的调试信息
   - 确认找到了正确的输入框和发送按钮

## 如果仍然不工作

1. **检查豆包AI网站**：
   - 访问 https://www.doubao.com/chat/
   - 确认网站结构没有重大变化

2. **运行调试代码**：
   - 使用上述调试方法分析实际的DOM结构
   - 找到正确的选择器

3. **更新选择器**：
   - 根据调试结果更新 `dock_web_view.py` 中的选择器
   - 在第1144行和1145行更新 `class_name` 和 `button_class`

4. **调整时序**：
   - 如果网络较慢，可能需要增加延迟时间
   - 修改第1281行、1350行和1357行的延迟值

## 修改的文件
- `dock_web_view.py`：主要修改文件
- `test_doubao_debug.py`：调试工具（新增）
- `DOUBAO_AI_FIX_README.md`：说明文档（新增）

## 注意事项
- 修改后需要重启Anki才能生效
- 如果豆包AI网站更新，可能需要重新调整选择器
- 建议定期检查和更新选择器以保持兼容性
